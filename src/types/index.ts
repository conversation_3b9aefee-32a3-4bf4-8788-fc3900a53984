// Core types for IGDB-based game search and display functionality

export type Platform =
  | 'PC'
  | 'PlayStation 5'
  | 'PlayStation 4'
  | 'PlayStation 3'
  | 'PlayStation 2'
  | 'PlayStation'
  | 'PlayStation Vita'
  | 'Xbox Series X/S'
  | 'Xbox One'
  | 'Xbox 360'
  | 'Xbox'
  | 'Nintendo Switch'
  | 'Nintendo 3DS'
  | 'Nintendo DS'
  | 'Wii U'
  | 'Wii'
  | 'GameCube'
  | 'Nintendo 64'
  | 'iOS'
  | 'Android'
  | 'Steam Deck'
  | 'Mac'
  | 'Linux'
  | '3DO'
  | 'Arcade'
  | 'Sega Saturn'
  | 'Mobile';

export type Genre =
  | 'Action' | 'Adventure' | 'RPG' | 'Strategy' | 'Sports'
  | 'Racing' | 'Simulation' | 'Puzzle' | 'Fighting' | 'Shooter'
  | 'Horror' | 'Platformer' | 'Indie' | 'MMORPG' | 'Arcade'
  | 'Visual Novel' | 'Card & Board Game';

// Game interface for IGDB-sourced search results
export interface Game {
  id: string;
  title: string;
  platforms?: string[];
  genres: string[];
  developer?: string;
  publisher?: string;
  release_date?: string;
  description?: string;
  cover_image?: string;
  screenshots?: string[];
  youtube_links?: string[];
  metacritic_score?: number;
  igdb_id?: string;
  // Pricing information
  price_current?: number;
  price_original?: number;
  on_sale?: boolean;
  // Optional context properties
  userContext?: {
    owned: boolean;
    status?: 'playing' | 'completed' | 'backlog' | 'wishlist' | 'owned';
    userRating?: number;
    dateAdded?: string;
    hoursPlayed?: number;
  };
  recommendationContext?: {
    reason: string;
    confidence: number;
    categories: string[];
    source?: string;
  };
}

// IGDB API response interface
export interface IGDBGame {
  id: number;
  name: string;
  platforms?: Array<{ id: number; name: string }>;
  genres?: Array<{ id: number; name: string }>;
  involved_companies?: Array<{
    company: { id: number; name: string };
    developer: boolean;
    publisher: boolean;
  }>;
  first_release_date?: number;
  summary?: string;
  cover?: { url: string; image_id: string };
  screenshots?: Array<{ url: string; image_id: string }>;
  aggregated_rating?: number;
  videos?: Array<{ video_id: string; name: string }>;
  similar_games?: Array<{ id: number; name: string }>;
  category?: number; // 0 = main game, 1 = DLC, 2 = expansion
  themes?: Array<{ id: number; name: string }>;
  keywords?: Array<{ id: number; name: string }>;
}

// IGDB search options interface
export interface IGDBSearchOptions {
  limit?: number;
  platforms?: string[];
  genres?: string[];
  minRating?: number;
  maxRating?: number;
  minYear?: number;
  maxYear?: number;
  sortBy?: 'relevance' | 'rating' | 'release_date' | 'name' | 'popularity';
  sortDirection?: 'asc' | 'desc';
  excludeDLC?: boolean;
  excludeExpansions?: boolean;
  searchType?: 'exact' | 'fuzzy' | 'smart';
}

// TheGamesDB API interfaces
export interface TheGamesDBResponse<T> {
  code: number;
  status: string;
  remaining_monthly_allowance: number;
  extra_allowance: number;
  data: T;
  include?: Record<string, unknown>;
  pages?: {
    previous: string;
    current: string;
    next: string;
  };
}

export interface TheGamesDBGame {
  id: number;
  game_title: string;
  release_date?: string;
  platform: number;
  players?: number;
  overview?: string;
  last_updated?: string;
  rating?: string;
  coop?: string;
  youtube?: string;
  os?: string;
  processor?: string;
  ram?: string;
  hdd?: string;
  video?: string;
  sound?: string;
  developers?: number[];
  genres?: number[];
  publishers?: number[];
  alternates?: string[];
}

export interface TheGamesDBImage {
  id: number;
  type: 'boxart' | 'screenshot' | 'fanart' | 'banner' | 'clearlogo' | 'titlescreen';
  side?: 'front' | 'back';
  filename: string;
  resolution?: string;
}

export interface TheGamesDBImageBaseUrl {
  original: string;
  small: string;
  thumb: string;
  cropped_center_thumb: string;
  medium: string;
  large: string;
}

export interface TheGamesDBPlatform {
  id: number;
  name: string;
  alias: string;
  icon?: string;
  console?: string;
  controller?: string;
  developer?: string;
  overview?: string;
}

export interface TheGamesDBGenre {
  id: number;
  name: string;
}

export interface TheGamesDBDeveloper {
  id: number;
  name: string;
}

export interface TheGamesDBPublisher {
  id: number;
  name: string;
}

export interface TheGamesDBSearchOptions {
  limit?: number;
  platforms?: number[];
  fields?: string[];
  include?: string[];
  page?: number;
}

// RAWG API interfaces
export interface RAWGGame {
  id: number;
  slug: string;
  name: string;
  released?: string;
  tba: boolean;
  background_image?: string;
  rating: number;
  rating_top: number;
  ratings?: Array<{
    id: number;
    title: string;
    count: number;
    percent: number;
  }>;
  ratings_count: number;
  reviews_text_count: number;
  added: number;
  added_by_status?: {
    yet: number;
    owned: number;
    beaten: number;
    toplay: number;
    dropped: number;
    playing: number;
  };
  metacritic?: number;
  playtime: number;
  suggestions_count: number;
  updated: string;
  user_game?: unknown;
  reviews_count: number;
  saturated_color: string;
  dominant_color: string;
  platforms?: Array<{
    platform: {
      id: number;
      name: string;
      slug: string;
      image?: string;
      year_end?: number;
      year_start?: number;
      games_count: number;
      image_background?: string;
    };
    released_at?: string;
    requirements_en?: {
      minimum?: string;
      recommended?: string;
    };
    requirements_ru?: {
      minimum?: string;
      recommended?: string;
    };
  }>;
  parent_platforms?: Array<{
    platform: {
      id: number;
      name: string;
      slug: string;
    };
  }>;
  genres?: Array<{
    id: number;
    name: string;
    slug: string;
    games_count: number;
    image_background?: string;
  }>;
  stores?: Array<{
    id: number;
    store: {
      id: number;
      name: string;
      slug: string;
      domain?: string;
      games_count: number;
      image_background?: string;
    };
  }>;
  clip?: unknown;
  tags?: Array<{
    id: number;
    name: string;
    slug: string;
    language: string;
    games_count: number;
    image_background?: string;
  }>;
  esrb_rating?: {
    id: number;
    name: string;
    slug: string;
  };
  short_screenshots?: Array<{
    id: number;
    image: string;
  }>;
}

export interface RAWGSearchResponse {
  count: number;
  next?: string;
  previous?: string;
  results: RAWGGame[];
}

export interface RAWGSearchOptions {
  search?: string;
  page?: number;
  page_size?: number;
  platforms?: string;
  stores?: string;
  developers?: string;
  publishers?: string;
  genres?: string;
  tags?: string;
  creators?: string;
  dates?: string;
  updated?: string;
  platforms_count?: number;
  metacritic?: string;
  exclude_collection?: number;
  exclude_additions?: boolean;
  exclude_parents?: boolean;
  exclude_game_series?: boolean;
  exclude_stores?: string;
  ordering?: string;
}