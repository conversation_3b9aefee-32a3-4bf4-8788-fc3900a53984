import { enhancedAIService } from '@/lib/enhancedAIService';
import { supabase } from '@/lib/supabase';

export interface ArtworkSearchResult {
  id: string;
  title: string;
  url: string;
  source: 'igdb' | 'ai' | 'thegamesdb' | 'steamgriddb';
  quality: 'high' | 'medium' | 'low';
  isOriginal: boolean;
  artworkType?: 'front' | 'back' | 'spine' | 'disc' | 'manual' | '3d';
  dimensions?: { width: number; height: number };
  metadata?: {
    gameId?: number;
    platform?: string;
    region?: string;
    format?: string;
  };
}

export interface SearchOptions {
  sources?: ('igdb' | 'ai' | 'thegamesdb' | 'steamgriddb')[];
  artworkTypes?: string[];
  qualityFilter?: 'high' | 'medium' | 'low' | 'all';
  limit?: number;
  includeScreenshots?: boolean;
  preferredRegions?: string[];
  searchMode?: 'combined' | 'individual';
  perSourceLimit?: {
    igdb?: number;
    steamgriddb?: number;
    ai?: number;
  };
  enableProgressiveLoading?: boolean;
  loadMoreOffset?: number;
}

export interface IGDBCover {
  id: number;
  url: string;
  image_id: string;
  width?: number;
  height?: number;
  animated?: boolean;
}

export interface IGDBScreenshot {
  id: number;
  url: string;
  image_id: string;
  width?: number;
  height?: number;
}

export interface IGDBArtwork {
  id: number;
  url: string;
  image_id: string;
  width?: number;
  height?: number;
}

class ArtworkSearchService {
  private cache = new Map<string, { results: ArtworkSearchResult[]; timestamp: number }>();
  private failureCache = new Map<string, { timestamp: number; attempts: number }>();
  private activeRequests = new Map<string, Promise<ArtworkSearchResult[]>>();
  private readonly CACHE_DURATION = 30 * 60 * 1000; // 30 minutes
  private readonly FAILURE_COOLDOWN = 5 * 60 * 1000; // 5 minutes
  private readonly MAX_FAILURE_ATTEMPTS = 3;

  /**
   * Main search function that aggregates results from multiple sources
   * Uses direct service calls to avoid router complexity and API key issues
   */
  async searchArtwork(
    gameName: string, 
    options: SearchOptions = {}
  ): Promise<ArtworkSearchResult[]> {
    const {
      sources = ['igdb', 'steamgriddb', 'ai'],
      qualityFilter = 'all',
      limit = 100,
      searchMode = 'combined',
      _perSourceLimit = {
        igdb: 30,
        steamgriddb: 60,
        ai: 10
      },
      _enableProgressiveLoading = false,
      _loadMoreOffset = 0
    } = options;

    // Check cache first
    const cacheKey = this.getCacheKey(gameName, options);
    const cached = this.getFromCache(cacheKey);
    if (cached) {
      console.log(`📋 Using cached results for "${gameName}"`);
      return this.filterAndLimitResults(cached, qualityFilter, limit);
    }

    // Check if there's already an active request for this search
    const existingRequest = this.activeRequests.get(cacheKey);
    if (existingRequest) {
      console.log(`⏳ Request already in progress for "${gameName}", waiting...`);
      const results = await existingRequest;
      return this.filterAndLimitResults(results, qualityFilter, limit);
    }

    // Check failure cache to prevent spam (but allow at least one attempt)
    const failureEntry = this.failureCache.get(cacheKey);
    if (failureEntry && failureEntry.attempts >= this.MAX_FAILURE_ATTEMPTS) {
      const timeSinceLastFailure = Date.now() - failureEntry.timestamp;
      if (timeSinceLastFailure < this.FAILURE_COOLDOWN) {
        console.warn(`🚫 Skipping search for "${gameName}" - too many recent failures (${failureEntry.attempts}). Try again in ${Math.round((this.FAILURE_COOLDOWN - timeSinceLastFailure) / 60000)} minutes.`);
        return [];
      } else {
        // Reset failure cache after cooldown
        console.log(`🔄 Failure cooldown expired for "${gameName}", allowing retry`);
        this.failureCache.delete(cacheKey);
      }
    }

    console.log(`🎨 Starting ${searchMode} artwork search for "${gameName}" with sources:`, sources);

    // Handle individual source search mode
    if (searchMode === 'individual' && sources.length === 1) {
      const singleSource = sources[0];
      console.log(`🔍 Individual search mode: searching ${singleSource} only`);
      const searchPromise = this.searchSpecificSource(gameName, singleSource, options);
      this.activeRequests.set(cacheKey, searchPromise);

      try {
        const results = await searchPromise;
        return this.filterAndLimitResults(results, qualityFilter, limit);
      } finally {
        this.activeRequests.delete(cacheKey);
      }
    }

    // Create a promise for this search and store it to prevent duplicates
    const searchPromise = this.executeSearch(gameName, options);
    this.activeRequests.set(cacheKey, searchPromise);

    try {
      const results = await searchPromise;
      return this.filterAndLimitResults(results, qualityFilter, limit);
    } finally {
      // Clean up the active request
      this.activeRequests.delete(cacheKey);
    }
  }

  /**
   * Execute the actual search logic
   */
  private async executeSearch(gameName: string, options: SearchOptions): Promise<ArtworkSearchResult[]> {
    return this.fallbackToDirectSearch(gameName, options);
  }

  /**
   * Search a specific source only (for individual mode)
   */
  private async searchSpecificSource(
    gameName: string, 
    source: 'igdb' | 'steamgriddb' | 'ai', 
    options: SearchOptions
  ): Promise<ArtworkSearchResult[]> {
    const { 
      includeScreenshots = false, 
      perSourceLimit = { igdb: 30, steamgriddb: 60, ai: 10 } 
    } = options;

    console.log(`🎯 Searching ${source} specifically with enhanced limits`);
    
    try {
      switch (source) {
        case 'igdb':
          return await this.searchIGDBCovers(gameName, includeScreenshots, perSourceLimit.igdb);
        case 'steamgriddb':
          return await this.searchSteamGridDB(gameName, perSourceLimit.steamgriddb);
        case 'ai':
          return await this.searchAIEnhanced(gameName, perSourceLimit.ai);
        default:
          console.warn(`Unknown source: ${source}`);
          return [];
      }
    } catch (error) {
      console.error(`Error searching ${source}:`, error);
      return [];
    }
  }

  /**
   * Process router results and convert to artwork search results
   */
  private async processRouterResult(
    routerResult: { success: boolean; data?: unknown; error?: string; service: string },
    requestData: { gameName: string; includeScreenshots?: boolean; artworkTypes?: string[]; service?: string }
  ): Promise<ArtworkSearchResult[]> {
    const { gameName, includeScreenshots, artworkTypes: _artworkTypes, service } = requestData;

    // For now, we'll delegate to the original service-specific methods
    // In a full implementation, these would be integrated into the router itself
    switch (service) {
      case 'igdb':
        return this.searchIGDBCovers(gameName, includeScreenshots || false);
      case 'steamgriddb':
        return this.searchSteamGridDB(gameName);
      default:
        return this.searchAIEnhanced(gameName);
    }
  }

  /**
   * Fallback to direct service calls when router fails
   */
  private async fallbackDirectCall(requestData: { gameName: string; includeScreenshots?: boolean; artworkTypes?: string[]; service?: string }): Promise<ArtworkSearchResult[]> {
    const { gameName, includeScreenshots, artworkTypes: _artworkTypes, service } = requestData;
    
    console.log(`🔄 Falling back to direct call for ${service}`);

    try {
      switch (service) {
        case 'igdb':
          return await this.searchIGDBCovers(gameName, includeScreenshots || false);
        case 'steamgriddb':
          return await this.searchSteamGridDB(gameName);
        default:
          return await this.searchAIEnhanced(gameName);
      }
    } catch (error) {
      console.error(`Fallback also failed for ${service}:`, error);
      
      // If it's an API key issue, don't spam the logs
      if (error instanceof Error && 
          (error.message.includes('No available services with valid API keys') ||
           error.message.includes('No API key available') ||
           error.message.includes('IGDB search error'))) {
        console.warn(`🚫 Direct fallback failed due to configuration issues for ${service}`);
      }
      
      return [];
    }
  }

  /**
   * Complete fallback to original search method
   */
  private async fallbackToDirectSearch(
    gameName: string, 
    options: SearchOptions
  ): Promise<ArtworkSearchResult[]> {
    console.log(`🔄 Complete fallback to direct search for "${gameName}"`);
    
    const {
      sources = ['igdb', 'steamgriddb', 'ai'],
      qualityFilter = 'all',
      limit = 100,
      includeScreenshots = false,
      preferredRegions = ['US', 'EU', 'JP'],
      perSourceLimit = {
        igdb: 30,
        steamgriddb: 60,
        ai: 10
      }
    } = options;

    const allResults: ArtworkSearchResult[] = [];
    const searchPromises: Promise<ArtworkSearchResult[]>[] = [];

    // Direct API calls as fallback with enhanced limits
    if (sources.includes('igdb')) {
      searchPromises.push(this.searchIGDBCovers(gameName, includeScreenshots, perSourceLimit.igdb));
    }
    if (sources.includes('steamgriddb')) {
      searchPromises.push(this.searchSteamGridDB(gameName, perSourceLimit.steamgriddb));
    }
    if (sources.includes('ai')) {
      searchPromises.push(this.searchAIEnhanced(gameName, perSourceLimit.ai));
    }

    try {
      const results = await Promise.allSettled(searchPromises);
      
      results.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          allResults.push(...result.value);
        } else {
          console.error(`Fallback search source ${sources[index]} failed:`, result.reason);
        }
      });

      const processedResults = this.processResults(allResults, preferredRegions);
      return this.filterAndLimitResults(processedResults, qualityFilter, limit);
    } catch (error) {
      console.error('Complete fallback search failed:', error);
      return [];
    }
  }

  /**
   * Search IGDB for official game covers and artworks
   */
  private async searchIGDBCovers(
    gameName: string, 
    includeScreenshots = false,
    maxResults = 30
  ): Promise<ArtworkSearchResult[]> {
    try {
      console.log(`🎮 Searching IGDB for "${gameName}"`);

      // Search for the game first with enhanced fields
      const gameQuery = `
        fields id, name, cover.url, cover.image_id, cover.width, cover.height, cover.animated,
               artworks.url, artworks.image_id, artworks.width, artworks.height,
               platforms.name, platforms.abbreviation,
               first_release_date
               ${includeScreenshots ? ', screenshots.url, screenshots.image_id, screenshots.width, screenshots.height' : ''};
        search "${gameName}";
        limit ${Math.min(maxResults, 50)};
      `;

      const { data, error } = await supabase.functions.invoke('igdb-proxy', {
        body: { endpoint: 'games', query: gameQuery }
      });

      if (error) {
        console.error('IGDB search error:', error);
        return [];
      }

      if (!data || !Array.isArray(data)) {
        console.warn('No IGDB data received');
        return [];
      }

      const results: ArtworkSearchResult[] = [];

      data.forEach((game: unknown) => {
        const gameData = game as {
          id: number;
          name: string;
          cover?: { id: number; url: string; width?: number; height?: number };
          artworks?: Array<{ id: number; url: string; width?: number; height?: number }>;
          screenshots?: Array<{ id: number; url: string; width?: number; height?: number }>;
        };
        // Add cover image with multiple sizes
        if (gameData.cover) {
          // Add high-resolution cover
          results.push({
            id: `igdb_cover_hd_${gameData.cover.id}`,
            title: `${gameData.name} - HD Cover`,
            url: this.convertIGDBImageUrl(gameData.cover.url, '720p'),
            source: 'igdb',
            quality: 'high',
            isOriginal: true,
            artworkType: 'front',
            dimensions: {
              width: gameData.cover.width || 600,
              height: gameData.cover.height || 800
            },
            metadata: {
              gameId: gameData.id,
              format: 'cover'
            }
          });

          // Add regular cover as fallback
          results.push({
            id: `igdb_cover_${gameData.cover.id}`,
            title: `${gameData.name} - Cover`,
            url: this.convertIGDBImageUrl(gameData.cover.url, 'cover_big'),
            source: 'igdb',
            quality: 'medium',
            isOriginal: true,
            artworkType: 'front',
            dimensions: {
              width: gameData.cover.width || 600,
              height: gameData.cover.height || 800
            },
            metadata: {
              gameId: gameData.id,
              format: 'cover'
            }
          });
        }

        // Add artworks
        if (gameData.artworks && Array.isArray(gameData.artworks)) {
          gameData.artworks.forEach((artwork) => {
            results.push({
              id: `igdb_artwork_${artwork.id}`,
              title: `${gameData.name} - Official Artwork`,
              url: this.convertIGDBImageUrl(artwork.url, '1080p'),
              source: 'igdb',
              quality: 'high',
              isOriginal: true,
              artworkType: 'front', // Could be enhanced to detect type
              dimensions: {
                width: artwork.width || 1920,
                height: artwork.height || 1080
              },
              metadata: {
                gameId: gameData.id,
                format: 'artwork'
              }
            });
          });
        }

        // Add screenshots if requested
        if (includeScreenshots && gameData.screenshots && Array.isArray(gameData.screenshots)) {
          gameData.screenshots.slice(0, 3).forEach((screenshot) => {
            results.push({
              id: `igdb_screenshot_${screenshot.id}`,
              title: `${gameData.name} - Screenshot`,
              url: this.convertIGDBImageUrl(screenshot.url, '1080p'),
              source: 'igdb',
              quality: 'medium',
              isOriginal: false,
              artworkType: 'front',
              dimensions: {
                width: screenshot.width || 1920,
                height: screenshot.height || 1080
              },
              metadata: {
                gameId: gameData.id,
                format: 'screenshot'
              }
            });
          });
        }
      });

      console.log(`✅ Found ${results.length} IGDB results for "${gameName}"`);
      return results;

    } catch (error) {
      console.error('Error searching IGDB:', error);
      return [];
    }
  }

  /**
   * Search SteamGridDB for community-curated artwork
   */
  private async searchSteamGridDB(gameName: string, maxResults = 60): Promise<ArtworkSearchResult[]> {
    try {
      console.log(`🎨 Searching SteamGridDB for "${gameName}"`);

      // First, search for the game to get the game ID
      const gameSearchResult = await supabase.functions.invoke('steamgriddb-proxy', {
        body: { 
          endpoint: '/search/autocomplete',
          params: { term: gameName }
        },
        headers: {
          'X-API-Key': import.meta.env.VITE_STEAMGRIDDB_API_KEY
        }
      });

      if (gameSearchResult.error || !gameSearchResult.data || gameSearchResult.data.length === 0) {
        console.warn('No games found in SteamGridDB for:', gameName);
        return [];
      }

      // Get the first matching game
      const game = gameSearchResult.data[0];
      const gameId = game.id;
      console.log(`Found SteamGridDB game: ${game.name} (ID: ${gameId})`);

      const allResults: ArtworkSearchResult[] = [];

      // Search for different types of artwork with enhanced limits
      const perTypeLimit = Math.ceil(maxResults / 4); // Distribute across 4 artwork types
      const artworkTypes = [
        { endpoint: `/grids/game/${gameId}`, type: 'front', label: 'Grid' },
        { endpoint: `/heroes/game/${gameId}`, type: 'front', label: 'Hero' },
        { endpoint: `/logos/game/${gameId}`, type: 'front', label: 'Logo' },
        { endpoint: `/icons/game/${gameId}`, type: 'front', label: 'Icon' }
      ];
      
      console.log(`📊 Fetching ${perTypeLimit} results per artwork type (${artworkTypes.length} types)`);

      for (const artworkType of artworkTypes) {
        try {
          const artworkResult = await supabase.functions.invoke('steamgriddb-proxy', {
            body: { 
              endpoint: artworkType.endpoint,
              params: { 
                dimensions: ['600x900', '460x215', '920x430'], // Various common sizes
                limit: Math.min(perTypeLimit, 25) // Cap per-type limit at 25 for API efficiency
              }
            },
            headers: {
              'X-API-Key': import.meta.env.VITE_STEAMGRIDDB_API_KEY
            }
          });

          if (artworkResult.data && Array.isArray(artworkResult.data)) {
            const artworks = artworkResult.data.map((artwork: { id?: string | number; url: string; width?: number; height?: number; style?: string; author?: { name?: string } }, index: number) => ({
              id: `steamgriddb_${artworkType.label.toLowerCase()}_${artwork.id || index}`,
              title: `${game.name} - ${artworkType.label}`,
              url: artwork.url,
              source: 'steamgriddb' as const,
              quality: this.assessSteamGridDBQuality(artwork),
              isOriginal: artwork.style === 'official',
              artworkType: artworkType.type as const,
              dimensions: {
                width: artwork.width || 600,
                height: artwork.height || 900
              },
              metadata: {
                gameId: gameId,
                format: artworkType.label.toLowerCase(),
                style: artwork.style,
                author: artwork.author?.name
              }
            }));

            allResults.push(...artworks);
          }
        } catch (error) {
          console.error(`Error fetching ${artworkType.label} from SteamGridDB:`, error);
        }
      }

      console.log(`✅ Found ${allResults.length} SteamGridDB results for "${gameName}"`);
      return allResults;

    } catch (error) {
      console.error('Error searching SteamGridDB:', error);
      return [];
    }
  }

  /**
   * Assess SteamGridDB artwork quality based on metadata
   */
  private assessSteamGridDBQuality(artwork: { width?: number; height?: number; style?: string }): 'high' | 'medium' | 'low' {
    // High quality indicators
    if (artwork.style === 'official' || artwork.width >= 600 && artwork.height >= 800) {
      return 'high';
    }
    
    // Medium quality for community content with good dimensions
    if (artwork.width >= 300 && artwork.height >= 400) {
      return 'medium';
    }
    
    return 'low';
  }

  /**
   * AI-enhanced search with intelligent query expansion
   */
  private async searchAIEnhanced(gameName: string, maxResults = 10): Promise<ArtworkSearchResult[]> {
    try {
      console.log(`🤖 AI-enhanced search for "${gameName}"`);

      // Use the single search method and generate additional results if needed
      const baseResults = await enhancedAIService.searchGameBoxArt(gameName);
      
      // Generate additional placeholder results to meet maxResults requirement
      const allResults = [...baseResults];
      while (allResults.length < maxResults) {
        const index = allResults.length;
        allResults.push({
          title: `${gameName} - AI Generated Variant ${index + 1}`,
          url: `https://via.placeholder.com/600x800/4F46E5/FFFFFF?text=${encodeURIComponent(gameName + ' ' + (index + 1))}`,
          source: 'Generated Placeholder',
          quality: index % 3 === 0 ? 'high' : index % 2 === 0 ? 'medium' : 'low' as 'high' | 'medium' | 'low',
          isOriginal: false
        });
      }
      
      const mappedResults = allResults.slice(0, maxResults).map((result, index) => ({
        id: `ai_${gameName.replace(/\s+/g, '_')}_${index}`,
        title: result.title,
        url: result.url,
        source: 'ai' as const,
        quality: result.quality,
        isOriginal: result.isOriginal,
        artworkType: 'front' as const,
        metadata: {
          originalQuery: gameName,
          format: 'ai_generated'
        }
      }));

      console.log(`✅ Found ${mappedResults.length} AI-enhanced results for "${gameName}"`);
      return mappedResults;

    } catch (error) {
      console.error('Error in AI-enhanced search:', error);
      return [];
    }
  }

  /**
   * Generate expanded search queries using AI
   */
  private async generateExpandedQueries(gameName: string): Promise<string[]> {
    const queries = [gameName]; // Always include original

    try {
      // Add common variations
      const variations = [
        `${gameName} box art`,
        `${gameName} cover art`,
        `${gameName} game cover`,
        `${gameName} official artwork`
      ];

      queries.push(...variations);

      // Add platform-specific variations if we can detect platform context
      const platformVariations = [
        `${gameName} PlayStation`,
        `${gameName} Xbox`,
        `${gameName} Nintendo`,
        `${gameName} PC Steam`
      ];

      // Only add 2 platform variations to avoid too many queries
      queries.push(...platformVariations.slice(0, 2));

    } catch (error) {
      console.error('Error generating expanded queries:', error);
    }

    return queries.slice(0, 6); // Limit to 6 queries max
  }

  /**
   * Process and deduplicate results
   */
  private processResults(
    results: ArtworkSearchResult[], 
    preferredRegions: string[]
  ): ArtworkSearchResult[] {
    // Remove duplicates based on URL
    const uniqueResults = results.filter((result, index, self) =>
      index === self.findIndex(r => r.url === result.url)
    );

    // Apply preferred regions filter if provided
    console.log('Processing results with preferred regions:', preferredRegions);

    // Sort by quality and source priority
    return uniqueResults.sort((a, b) => {
      // Prioritize IGDB (official) sources first
      if (a.source === 'igdb' && b.source !== 'igdb') return -1;
      if (a.source !== 'igdb' && b.source === 'igdb') return 1;

      // Then prioritize SteamGridDB (community-curated) over AI
      if (a.source === 'steamgriddb' && b.source === 'ai') return -1;
      if (a.source === 'ai' && b.source === 'steamgriddb') return 1;

      // Then prioritize original artwork
      if (a.isOriginal && !b.isOriginal) return -1;
      if (!a.isOriginal && b.isOriginal) return 1;

      // Then prioritize quality
      const qualityOrder = { high: 3, medium: 2, low: 1 };
      const qualityDiff = qualityOrder[b.quality] - qualityOrder[a.quality];
      if (qualityDiff !== 0) return qualityDiff;

      return 0;
    });
  }

  /**
   * Filter and limit results based on options
   */
  private filterAndLimitResults(
    results: ArtworkSearchResult[],
    qualityFilter: string,
    limit: number
  ): ArtworkSearchResult[] {
    let filtered = results;

    // Apply quality filter
    if (qualityFilter !== 'all') {
      filtered = filtered.filter(r => r.quality === qualityFilter);
    }

    // Apply limit
    return filtered.slice(0, limit);
  }

  /**
   * Convert IGDB image URL to desired size
   */
  private convertIGDBImageUrl(url: string, size: string): string {
    if (!url) return '';
    
    // Remove the leading '//' and add https
    const cleanUrl = url.startsWith('//') ? url.substring(2) : url;
    
    // Replace 'thumb' with desired size
    return `https://${cleanUrl.replace(/t_thumb/, `t_${size}`)}`;
  }

  /**
   * Cache management
   */
  private getCacheKey(gameName: string, options: SearchOptions): string {
    return `${gameName}_${JSON.stringify(options)}`;
  }

  private getFromCache(key: string): ArtworkSearchResult[] | null {
    const cached = this.cache.get(key);
    if (!cached) return null;

    // Check if cache is still valid
    if (Date.now() - cached.timestamp > this.CACHE_DURATION) {
      this.cache.delete(key);
      return null;
    }

    return cached.results;
  }

  /**
   * Clear old cache entries
   */
  clearExpiredCache(): void {
    const now = Date.now();
    for (const [key, value] of this.cache.entries()) {
      if (now - value.timestamp > this.CACHE_DURATION) {
        this.cache.delete(key);
      }
    }
    
    // Also clear old failure cache entries
    for (const [key, failure] of this.failureCache.entries()) {
      if (now - failure.timestamp > this.FAILURE_COOLDOWN) {
        this.failureCache.delete(key);
      }
    }
  }

  /**
   * Track search failure to prevent spam
   */
  private trackSearchFailure(cacheKey: string): void {
    const existingFailure = this.failureCache.get(cacheKey);
    if (existingFailure) {
      this.failureCache.set(cacheKey, {
        timestamp: Date.now(),
        attempts: existingFailure.attempts + 1
      });
    } else {
      this.failureCache.set(cacheKey, {
        timestamp: Date.now(),
        attempts: 1
      });
    }
  }
}

// Export singleton instance
export const artworkSearchService = new ArtworkSearchService();
export default artworkSearchService;