import { supabase } from './supabase';
import { userGamesAPI } from './api/collections';


export type AIProvider = 'gemini' | 'openai' | 'deepseek';

export interface ChatMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  metadata?: {
    gameContext?: string;
    searchQuery?: string;
  };
}

export interface Conversation {
  id: string;
  userId: string;
  title: string;
  messages: ChatMessage[];
  createdAt: Date;
  updatedAt: Date;
}


export interface AIServiceConfig {
  provider: AIProvider;
  apiKey: string;
  model?: string;
  temperature?: number;
  maxTokens?: number;
}

class EnhancedAIService {
  private config: AIServiceConfig;

  constructor() {
    // Auto-detect available provider
    this.config = this.detectBestProvider();
  }

  /**
   * Auto-detect the best available AI provider
   */
  private detectBestProvider(): AIServiceConfig {
    const geminiKey = import.meta.env.VITE_GEMINI_API_KEY;
    const openaiKey = import.meta.env.VITE_OPENAI_API_KEY;
    const deepseekKey = import.meta.env.VITE_DEEPSEEK_API_KEY;

    if (openaiKey) {
      return {
        provider: 'openai',
        apiKey: openaiKey,
        model: 'gpt-4',
        temperature: 0.7,
        maxTokens: 2048
      };
    }

    if (deepseekKey) {
      return {
        provider: 'deepseek',
        apiKey: deepseekKey,
        model: 'deepseek-chat',
        temperature: 0.7,
        maxTokens: 2048
      };
    }

    if (geminiKey) {
      return {
        provider: 'gemini',
        apiKey: geminiKey,
        model: 'gemini-1.5-flash-latest',
        temperature: 0.7,
        maxTokens: 2048
      };
    }

    throw new Error('No AI provider API key found. Please set VITE_OPENAI_API_KEY, VITE_DEEPSEEK_API_KEY, or VITE_GEMINI_API_KEY');
  }

  /**
   * Set AI provider manually
   */
  setProvider(provider: AIProvider): void {
    const keys = {
      gemini: import.meta.env.VITE_GEMINI_API_KEY,
      openai: import.meta.env.VITE_OPENAI_API_KEY,
      deepseek: import.meta.env.VITE_DEEPSEEK_API_KEY
    };

    if (!keys[provider]) {
      throw new Error(`API key for ${provider} not found in environment variables`);
    }

    this.config = {
      provider,
      apiKey: keys[provider],
      model: this.getDefaultModel(provider),
      temperature: 0.7,
      maxTokens: 2048
    };
  }

  private getDefaultModel(provider: AIProvider): string {
    switch (provider) {
      case 'openai':
        return 'gpt-4';
      case 'deepseek':
        return 'deepseek-chat';
      case 'gemini':
        return 'gemini-1.5-flash-latest';
      default:
        throw new Error(`Unknown provider: ${provider}`);
    }
  }

  /**
   * Send a chat message and get AI response
   */
  async sendChatMessage(
    conversationId: string,
    userMessage: string,
    context?: {
      gameContext?: string;
      searchQuery?: string;
    }
  ): Promise<ChatMessage> {
    try {
      // Get conversation history
      const conversation = await this.getConversation(conversationId);
      
      // Add user message to conversation
      const userChatMessage: ChatMessage = {
        id: crypto.randomUUID(),
        role: 'user',
        content: userMessage,
        timestamp: new Date(),
        metadata: context
      };

      await this.addMessageToConversation(conversationId, userChatMessage);

      // Generate AI response based on context
      let aiResponse: string;
      const userId = conversation.userId;

      if (context?.gameContext) {
        aiResponse = await this.handleGameDiscussion(userMessage, conversation, context.gameContext, userId);
      } else {
        aiResponse = await this.handleGeneralConversation(userMessage, conversation, userId);
      }

      // Create AI response message
      const aiChatMessage: ChatMessage = {
        id: crypto.randomUUID(),
        role: 'assistant',
        content: aiResponse,
        timestamp: new Date()
      };

      await this.addMessageToConversation(conversationId, aiChatMessage);

      return aiChatMessage;
    } catch (error) {
      console.error('Error in sendChatMessage:', error);
      throw new Error('Failed to process chat message');
    }
  }


  /**
   * Handle game-specific discussions
   */
  private async handleGameDiscussion(
    userMessage: string,
    conversation: Conversation,
    gameContext: string,
    userId?: string
  ): Promise<string> {
    const prompt = await this.buildGameDiscussionPrompt(userMessage, conversation.messages, gameContext, userId);
    return await this.callAIProvider(prompt);
  }

  /**
   * Handle general gaming conversations
   */
  private async handleGeneralConversation(userMessage: string, conversation: Conversation, userId?: string): Promise<string> {
    const prompt = await this.buildGeneralConversationPrompt(userMessage, conversation.messages, userId);
    return await this.callAIProvider(prompt);
  }





  /**
   * Call the configured AI provider
   */
  private async callAIProvider(prompt: string): Promise<string> {
    switch (this.config.provider) {
      case 'openai':
        return await this.callOpenAI(prompt);
      case 'deepseek':
        return await this.callDeepSeek(prompt);
      case 'gemini':
        return await this.callGemini(prompt);
      default:
        throw new Error(`Unsupported provider: ${this.config.provider}`);
    }
  }

  /**
   * Call OpenAI API
   */
  private async callOpenAI(prompt: string): Promise<string> {
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.config.apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: this.config.model,
        messages: [{ role: 'user', content: prompt }],
        temperature: this.config.temperature,
        max_tokens: this.config.maxTokens
      })
    });

    if (!response.ok) {
      throw new Error(`OpenAI API error: ${response.status}`);
    }

    const data = await response.json();
    return data.choices[0]?.message?.content || 'No response generated';
  }

  /**
   * Call DeepSeek API
   */
  private async callDeepSeek(prompt: string): Promise<string> {
    const response = await fetch('https://api.deepseek.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${this.config.apiKey}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        model: this.config.model,
        messages: [{ role: 'user', content: prompt }],
        temperature: this.config.temperature,
        max_tokens: this.config.maxTokens
      })
    });

    if (!response.ok) {
      throw new Error(`DeepSeek API error: ${response.status}`);
    }

    const data = await response.json();
    return data.choices[0]?.message?.content || 'No response generated';
  }

  /**
   * Call Gemini API
   */
  private async callGemini(prompt: string): Promise<string> {
    const response = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/${this.config.model}:generateContent?key=${this.config.apiKey}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        contents: [{
          parts: [{ text: prompt }]
        }],
        generationConfig: {
          temperature: this.config.temperature,
          maxOutputTokens: this.config.maxTokens
        }
      })
    });

    if (!response.ok) {
      throw new Error(`Gemini API error: ${response.status}`);
    }

    const data = await response.json();
    return data.candidates?.[0]?.content?.parts?.[0]?.text || 'No response generated';
  }

  /**
   * Get user's gaming context (library and wishlist)
   */
  private async getUserGamingContext(userId: string): Promise<string> {
    try {
      const { data: userGames, error } = await userGamesAPI.getUserCollection(userId);
      
      if (error) {
        console.error('Error fetching user games:', error);
        return '';
      }

      if (!userGames || userGames.length === 0) {
        return 'User has not added any games to their collection yet.';
      }

      // Separate games by status
      const libraryGames = userGames.filter(ug => ug.status === 'completed' || ug.status === 'playing' || ug.status === 'owned');
      const wishlistGames = userGames.filter(ug => ug.status === 'wishlist');
      const playingGames = userGames.filter(ug => ug.status === 'playing');

      let context = '';

      if (libraryGames.length > 0) {
        context += `User's Library (${libraryGames.length} games):\n`;
        libraryGames.slice(0, 15).forEach(ug => {
          if (ug.game) {
            context += `- ${ug.game.name} (${ug.game.release_date?.split('-')[0] || 'Unknown year'})`;
            if (ug.status === 'playing') context += ' [Currently Playing]';
            if (ug.status === 'completed') context += ' [Completed]';
            if (ug.game.platforms) context += ` - ${ug.game.platforms.slice(0, 2).join(', ')}`;
            context += '\n';
          }
        });
        context += '\n';
      }

      if (wishlistGames.length > 0) {
        context += `User's Wishlist (${wishlistGames.length} games):\n`;
        wishlistGames.slice(0, 10).forEach(ug => {
          if (ug.game) {
            context += `- ${ug.game.name} (${ug.game.release_date?.split('-')[0] || 'Unknown year'})`;
            if (ug.game.platforms) context += ` - ${ug.game.platforms.slice(0, 2).join(', ')}`;
            context += '\n';
          }
        });
        context += '\n';
      }

      if (playingGames.length > 0) {
        context += `Currently Playing (${playingGames.length} games):\n`;
        playingGames.forEach(ug => {
          if (ug.game) {
            context += `- ${ug.game.name}`;
            if (ug.hours_played) context += ` (${ug.hours_played} hours played)`;
            context += '\n';
          }
        });
        context += '\n';
      }

      return context.trim();
    } catch (error) {
      console.error('Error getting user gaming context:', error);
      return '';
    }
  }

  /**
   * Build prompt for game discussions
   */
  private async buildGameDiscussionPrompt(userMessage: string, history: ChatMessage[], gameContext: string, userId?: string): Promise<string> {
    const recentHistory = history.slice(-6).map(msg => `${msg.role}: ${msg.content}`).join('\n');
    const userGamingContext = userId ? await this.getUserGamingContext(userId) : '';
    
    return `You are a knowledgeable gaming AI assistant helping users discuss games. You have deep knowledge about games, their mechanics, lore, reviews, and the gaming industry.

Current game context: ${gameContext}

${userGamingContext ? `User's Gaming Profile:
${userGamingContext}

Use this information to provide personalized recommendations and insights. Reference their library, wishlist, or currently playing games when relevant. Consider their gaming preferences based on their collection.

` : ''}Recent conversation:
${recentHistory}

User's current message: ${userMessage}

Please provide a helpful, engaging response about the game. Be conversational, knowledgeable, and enthusiastic about gaming. If the user asks about recommendations, consider their gaming profile and suggest games that align with their preferences. Reference games from their library or wishlist when making comparisons or suggestions.

Response:`;
  }

  /**
   * Build prompt for general conversations
   */
  private async buildGeneralConversationPrompt(userMessage: string, history: ChatMessage[], userId?: string): Promise<string> {
    const recentHistory = history.slice(-8).map(msg => `${msg.role}: ${msg.content}`).join('\n');
    const userGamingContext = userId ? await this.getUserGamingContext(userId) : '';
    
    return `You are an enthusiastic gaming AI assistant and companion. You help users discover games, discuss gaming topics, and provide personalized recommendations. You're knowledgeable about all aspects of gaming including:

- Game recommendations based on preferences
- Game discovery and exploration
- Gaming history and industry knowledge
- Gaming tips and strategies
- Platform-specific advice
- Indie and AAA game insights

${userGamingContext ? `User's Gaming Profile:
${userGamingContext}

Use this information to provide highly personalized recommendations and insights. When suggesting games, consider what they already own, what's on their wishlist, and what they're currently playing. Make connections between games in their collection and potential new discoveries.

` : ''}Recent conversation:
${recentHistory}

User's current message: ${userMessage}

Provide a helpful, engaging, and enthusiastic response. Be conversational and show genuine interest in gaming. When making recommendations, consider their existing game collection and preferences shown in their gaming profile. Focus on discussing games, gameplay mechanics, recommendations, and helping users explore new gaming experiences.

Response:`;
  }

  /**
   * Create a new conversation
   */
  async createConversation(userId: string, title?: string): Promise<string> {
    const conversationId = crypto.randomUUID();
    const conversation: Conversation = {
      id: conversationId,
      userId,
      title: title || 'New Chat',
      messages: [],
      createdAt: new Date(),
      updatedAt: new Date()
    };

    try {
      const { error } = await supabase.from('ai_conversations').insert({
        id: conversation.id,
        user_id: conversation.userId,
        title: conversation.title,
        messages: conversation.messages,
        created_at: conversation.createdAt.toISOString(),
        updated_at: conversation.updatedAt.toISOString()
      });

      if (error) throw error;
      return conversationId;
    } catch (error) {
      console.error('Error creating conversation:', error);
      throw new Error('Failed to create conversation');
    }
  }

  /**
   * Get conversation by ID
   */
  async getConversation(conversationId: string): Promise<Conversation> {
    try {
      const { data, error } = await supabase
        .from('ai_conversations')
        .select('*')
        .eq('id', conversationId)
        .single();

      if (error) throw error;

      return {
        id: data.id,
        userId: data.user_id,
        title: data.title,
        messages: data.messages || [],
        createdAt: new Date(data.created_at),
        updatedAt: new Date(data.updated_at)
      };
    } catch (error) {
      console.error('Error getting conversation:', error);
      throw new Error('Conversation not found');
    }
  }

  /**
   * Get user conversations
   */
  async getUserConversations(userId: string): Promise<Conversation[]> {
    try {
      const { data, error } = await supabase
        .from('ai_conversations')
        .select('*')
        .eq('user_id', userId)
        .order('updated_at', { ascending: false });

      if (error) throw error;

      return data.map((conv: {
        id: string;
        user_id: string;
        title: string;
        messages: ChatMessage[];
        created_at: string;
        updated_at: string;
      }) => ({
        id: conv.id,
        userId: conv.user_id,
        title: conv.title,
        messages: conv.messages || [],
        createdAt: new Date(conv.created_at),
        updatedAt: new Date(conv.updated_at)
      }));
    } catch (error) {
      console.error('Error getting user conversations:', error);
      return [];
    }
  }

  /**
   * Add message to conversation
   */
  async addMessageToConversation(conversationId: string, message: ChatMessage): Promise<void> {
    try {
      const conversation = await this.getConversation(conversationId);
      conversation.messages.push(message);
      conversation.updatedAt = new Date();

      const { error } = await supabase
        .from('ai_conversations')
        .update({
          messages: conversation.messages,
          updated_at: conversation.updatedAt.toISOString()
        })
        .eq('id', conversationId);

      if (error) throw error;
    } catch (error) {
      console.error('Error adding message to conversation:', error);
      throw new Error('Failed to add message');
    }
  }

  /**
   * Delete conversation
   */
  async deleteConversation(conversationId: string): Promise<void> {
    try {
      const { error } = await supabase
        .from('ai_conversations')
        .delete()
        .eq('id', conversationId);

      if (error) throw error;
    } catch (error) {
      console.error('Error deleting conversation:', error);
      throw new Error('Failed to delete conversation');
    }
  }

  /**
   * Get current provider info
   */
  getCurrentProvider(): { provider: AIProvider; model: string } {
    return {
      provider: this.config.provider,
      model: this.config.model || 'unknown'
    };
  }
}

export const enhancedAIService = new EnhancedAIService();