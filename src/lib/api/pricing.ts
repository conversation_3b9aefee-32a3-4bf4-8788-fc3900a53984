import { supabase } from '../supabase';
import {
  PriceTrackingRecord,
  PriceTrackingUpdateData,
  PriceAlertRecord,
  PriceAlertUpdateData,
} from '../../types/database';

// Pricing interfaces
export interface PriceData {
  gameId: string;
  shopName: string;
  shopUrl: string;
  price: number;
  originalPrice?: number;
  currency: string;
  isOnSale: boolean;
  discountPercent: number;
  lastUpdated: string;
}

export interface DealInfo {
  gameId: string;
  gameName: string;
  shopName: string;
  shopUrl: string;
  currentPrice: number;
  originalPrice: number;
  discountPercent: number;
  currency: string;
  lastUpdated: string;
}

export interface PriceAlert {
  id: string;
  user_id: string;
  game_id: string;
  target_price: number;
  currency: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

/**
 * Price Tracking Database API
 * Handles price data storage and retrieval from the database
 */
export class PriceTrackingDatabaseAPI {
  /**
   * Get current prices for a game
   */
  async getGamePrices(gameId: string) {
    return supabase.from('price_tracking').select('*').eq('game_id', gameId).order('last_updated', { ascending: false });
  }

  /**
   * Get price history for a game
   */
  async getGamePriceHistory(gameId: string, days: number = 30) {
    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - days);
    return supabase
      .from('price_tracking')
      .select('*')
      .eq('game_id', gameId)
      .gte('last_updated', cutoffDate.toISOString())
      .order('last_updated', { ascending: true });
  }

  /**
   * Get lowest recorded price for a game
   */
  async getLowestPrice(gameId: string) {
    return supabase
      .from('price_tracking')
      .select('*')
      .eq('game_id', gameId)
      .order('price', { ascending: true })
      .limit(1)
      .single();
  }

  /**
   * Get current prices across all platforms for a game
   */
  async getCurrentPrices(gameId: string) {
    return supabase
      .from('price_tracking')
      .select('*')
      .eq('game_id', gameId)
      .order('last_updated', { ascending: false })
      .limit(10);
  }

  /**
   * Add a new price entry
   */
  async addPriceEntry(priceData: Omit<PriceTrackingRecord, 'id' | 'created_at'>) {
    return supabase.from('price_tracking').insert(priceData).select().single();
  }

  /**
   * Update an existing price entry
   */
  async updatePriceEntry(id: string, updates: PriceTrackingUpdateData) {
    return supabase.from('price_tracking').update(updates).eq('id', id).select().single();
  }

  /**
   * Get user's price alerts
   */
  async getPriceAlerts(userId: string) {
    return supabase
      .from('price_alerts')
      .select(`
        *,
        game:games(*)
      `)
      .eq('user_id', userId)
      .eq('is_active', true);
  }

  /**
   * Create a new price alert
   */
  async createPriceAlert(alertData: Omit<PriceAlertRecord, 'id' | 'created_at' | 'updated_at'>) {
    return supabase.from('price_alerts').insert(alertData).select().single();
  }

  /**
   * Update a price alert
   */
  async updatePriceAlert(id: string, updates: PriceAlertUpdateData) {
    return supabase.from('price_alerts').update(updates).eq('id', id).select().single();
  }

  /**
   * Delete a price alert
   */
  async deletePriceAlert(id: string) {
    return supabase.from('price_alerts').delete().eq('id', id);
  }

  /**
   * Get wishlist with current prices
   */
  async getWishlistWithPrices(userId: string) {
    try {
      // Ensure user records exist before querying
      await this.ensureUserRecords(userId);
      
      return supabase
        .from('user_games')
        .select(`
          *,
          game:games(*)
        `)
        .eq('user_id', userId)
        .eq('status', 'wishlist')
        .order('date_added', { ascending: false });
    } catch (error) {
      console.error('Error getting wishlist with prices:', error);
      throw error;
    }
  }

  /**
   * Helper function to ensure user records exist
   */
  private async ensureUserRecords(userId: string) {
    try {
      // Check if user profile exists
      const { error: profileError } = await supabase
        .from('user_profiles')
        .select('id')
        .eq('id', userId)
        .single();

      if (profileError && profileError.code === 'PGRST116') {
        // Profile doesn't exist, create it
        const { error: createProfileError } = await supabase
          .from('user_profiles')
          .insert({ id: userId });
        
        if (createProfileError) {
          console.warn('Failed to create user profile:', createProfileError);
        }
      }

      // Check if user preferences exist
      const { error: preferencesError } = await supabase
        .from('user_preferences')
        .select('id')
        .eq('user_id', userId)
        .single();

      if (preferencesError && preferencesError.code === 'PGRST116') {
        // Preferences don't exist, create them
        const { error: createPreferencesError } = await supabase
          .from('user_preferences')
          .insert({ user_id: userId });
        
        if (createPreferencesError) {
          console.warn('Failed to create user preferences:', createPreferencesError);
        }
      }
    } catch (error) {
      console.warn('Error ensuring user records:', error);
    }
  }
}

/**
 * Price Tracking Service
 * Handles external price tracking APIs and deal monitoring
 */
export class PriceTrackingService {
  private readonly STEAM_STORE_API = 'https://store.steampowered.com/api/appdetails';
  private readonly EPIC_STORE_API = 'https://store-site-backend-static.ak.epicgames.com/freeGamesPromotions';
  private readonly ISTHEREANYDEAL_API = 'https://api.isthereanydeal.com/v01';

  // Store mappings for different platforms
  private readonly storeMapping = {
    steam: 'Steam',
    epic: 'Epic Games Store',
    gog: 'GOG',
    origin: 'Origin',
    uplay: 'Ubisoft Connect',
    microsoft: 'Microsoft Store',
    playstation: 'PlayStation Store',
    nintendo: 'Nintendo eShop'
  };

  /**
   * Get current prices for a game from multiple stores
   */
  async getCurrentPricesFromStores(gameName: string, gameId?: string): Promise<PriceData[]> {
    const prices: PriceData[] = [];

    try {
      // Get Steam prices
      const steamPrice = await this.getSteamPrice(gameName, gameId);
      if (steamPrice) prices.push(steamPrice);

      // Get Epic Games Store prices
      const epicPrice = await this.getEpicPrice(gameName);
      if (epicPrice) prices.push(epicPrice);

      // Get GOG prices
      const gogPrice = await this.getGOGPrice(gameName);
      if (gogPrice) prices.push(gogPrice);

      // Use IsThereAnyDeal API for comprehensive price data
      const itadPrices = await this.getIsThereAnyDealPrices(gameName);
      prices.push(...itadPrices);

      return prices;
    } catch (error) {
      console.error('Error fetching current prices:', error);
      return [];
    }
  }

  /**
   * Get Steam price for a game
   */
  private async getSteamPrice(gameName: string, steamAppId?: string): Promise<PriceData | null> {
    try {
      if (!steamAppId) {
        // Would need to search for Steam app ID first
        return null;
      }

      const response = await fetch(`${this.STEAM_STORE_API}?appids=${steamAppId}&cc=US&l=en`);
      const data = await response.json();
      
      const gameData = data[steamAppId];
      if (!gameData?.success || !gameData.data?.price_overview) {
        return null;
      }

      const priceInfo = gameData.data.price_overview;
      
      return {
        gameId: steamAppId,
        shopName: 'Steam',
        shopUrl: `https://store.steampowered.com/app/${steamAppId}`,
        price: priceInfo.final / 100, // Steam prices are in cents
        originalPrice: priceInfo.initial / 100,
        currency: priceInfo.currency,
        isOnSale: priceInfo.discount_percent > 0,
        discountPercent: priceInfo.discount_percent,
        lastUpdated: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error fetching Steam price:', error);
      return null;
    }
  }

  /**
   * Get Epic Games Store price
   */
  private async getEpicPrice(gameName: string): Promise<PriceData | null> {
    try {
      // Epic Games Store API is more complex and requires authentication
      // For demonstration, we'll return mock data
      return {
        gameId: 'epic_mock',
        shopName: 'Epic Games Store',
        shopUrl: `https://store.epicgames.com/en-US/browse?q=${encodeURIComponent(gameName)}`,
        price: Math.floor(Math.random() * 60) + 10,
        originalPrice: Math.floor(Math.random() * 60) + 20,
        currency: 'USD',
        isOnSale: Math.random() > 0.5,
        discountPercent: Math.floor(Math.random() * 50),
        lastUpdated: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error fetching Epic price:', error);
      return null;
    }
  }

  /**
   * Get GOG price
   */
  private async getGOGPrice(gameName: string): Promise<PriceData | null> {
    try {
      // GOG API would require specific implementation
      // For demonstration, we'll return mock data
      return {
        gameId: 'gog_mock',
        shopName: 'GOG',
        shopUrl: `https://www.gog.com/games?search=${encodeURIComponent(gameName)}`,
        price: Math.floor(Math.random() * 50) + 5,
        originalPrice: Math.floor(Math.random() * 50) + 15,
        currency: 'USD',
        isOnSale: Math.random() > 0.6,
        discountPercent: Math.floor(Math.random() * 40),
        lastUpdated: new Date().toISOString()
      };
    } catch (error) {
      console.error('Error fetching GOG price:', error);
      return null;
    }
  }

  /**
   * Get prices from IsThereAnyDeal API
   */
  private async getIsThereAnyDealPrices(gameName: string): Promise<PriceData[]> {
    try {
      // IsThereAnyDeal API requires authentication
      // For demonstration, we'll return mock data
      
      const stores = ['Steam', 'Epic Games Store', 'GOG', 'Humble Store', 'GamesPlanet'];
      const prices: PriceData[] = [];
      
      for (const store of stores) {
        prices.push({
          gameId: 'mock',
          shopName: store,
          shopUrl: `https://isthereanydeal.com/search/?q=${encodeURIComponent(gameName)}`,
          price: Math.floor(Math.random() * 60) + 5,
          originalPrice: Math.floor(Math.random() * 60) + 15,
          currency: 'USD',
          isOnSale: Math.random() > 0.4,
          discountPercent: Math.floor(Math.random() * 60),
          lastUpdated: new Date().toISOString()
        });
      }
      
      return prices;
    } catch (error) {
      console.error('Error fetching IsThereAnyDeal prices:', error);
      return [];
    }
  }

  /**
   * Update price data for a game
   */
  async updateGamePrices(gameId: string, gameName: string): Promise<void> {
    try {
      const currentPrices = await this.getCurrentPricesFromStores(gameName, gameId);
      
      for (const priceData of currentPrices) {
        await supabase.from('price_tracking').insert({
          game_id: gameId,
          shop_name: priceData.shopName,
          shop_url: priceData.shopUrl,
          price: priceData.price,
          original_price: priceData.originalPrice,
          currency: priceData.currency,
          is_on_sale: priceData.isOnSale,
          discount_percent: priceData.discountPercent,
          last_updated: priceData.lastUpdated
        });
      }
    } catch (error) {
      console.error('Error updating game prices:', error);
      throw error;
    }
  }

  /**
   * Get price history for a game
   */
  async getPriceHistory(gameId: string, days: number = 30): Promise<PriceData[]> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - days);
      
      const { data, error } = await supabase
        .from('price_tracking')
        .select('*')
        .eq('game_id', gameId)
        .gte('last_updated', cutoffDate.toISOString())
        .order('last_updated', { ascending: true });

      if (error) throw error;

      return data.map(record => ({
        gameId: record.game_id,
        shopName: record.shop_name,
        shopUrl: record.shop_url,
        price: record.price,
        originalPrice: record.original_price,
        currency: record.currency,
        isOnSale: record.is_on_sale,
        discountPercent: record.discount_percent,
        lastUpdated: record.last_updated
      }));
    } catch (error) {
      console.error('Error getting price history:', error);
      return [];
    }
  }

  /**
   * Check for price alerts
   */
  async checkPriceAlerts(gameId: string): Promise<void> {
    try {
      // Get all active alerts for this game
      const { data: alerts, error: alertError } = await supabase
        .from('price_alerts')
        .select('*')
        .eq('game_id', gameId)
        .eq('is_active', true);

      if (alertError || !alerts || alerts.length === 0) return;

      // Get current prices
      const { data: currentPrices, error: priceError } = await supabase
        .from('price_tracking')
        .select('*')
        .eq('game_id', gameId)
        .order('last_updated', { ascending: false })
        .limit(10);

      if (priceError || !currentPrices) return;

      // Check each alert against current prices
      for (const alert of alerts) {
        const lowestCurrentPrice = Math.min(...currentPrices.map(p => p.price));
        
        if (lowestCurrentPrice <= alert.target_price) {
          // Price alert triggered - would send notification here
          console.log(`Price alert triggered for game ${gameId}: ${lowestCurrentPrice} <= ${alert.target_price}`);
          
          // Could implement notification logic here
          await this.triggerPriceAlert(alert, lowestCurrentPrice);
        }
      }
    } catch (error) {
      console.error('Error checking price alerts:', error);
    }
  }

  /**
   * Trigger a price alert (send notification)
   */
  private async triggerPriceAlert(alert: { game_id: string; target_price: number; currency: string }, currentPrice: number): Promise<void> {
    try {
      // Implementation would depend on notification system
      // For now, just log the alert
      console.log(`🚨 Price Alert: Game ${alert.game_id} is now ${currentPrice} ${alert.currency} (target: ${alert.target_price})`);
      
      // Could update alert status or send email/push notification
      // await this.sendPriceAlertNotification(alert, currentPrice);
    } catch (error) {
      console.error('Error triggering price alert:', error);
    }
  }

  /**
   * Get best deals for user's wishlist
   */
  async getBestDeals(userId: string, limit: number = 10): Promise<DealInfo[]> {
    try {
      const { data: wishlist, error } = await supabase
        .from('user_games')
        .select(`
          *,
          game:games(*)
        `)
        .eq('user_id', userId)
        .eq('status', 'wishlist');

      if (error) throw error;
      if (!wishlist || wishlist.length === 0) return [];
      
      const deals: DealInfo[] = [];
      
      for (const item of wishlist) {
        // Get current prices for this game
        const { data: prices, error: priceError } = await supabase
          .from('price_tracking')
          .select('*')
          .eq('game_id', item.game_id)
          .order('last_updated', { ascending: false })
          .limit(5);

        if (priceError || !prices || prices.length === 0) continue;
        
        // Find deals (games with discounts or low prices)
        for (const price of prices) {
          // Mock discount calculation - in real implementation, 
          // you'd compare with historical prices
          const isOnSale = price.is_on_sale;
          const discountPercent = price.discount_percent || 0;
          
          if (isOnSale && discountPercent > 10) {
            deals.push({
              gameId: item.game_id,
              gameName: item.game.name,
              shopName: price.shop_name,
              shopUrl: price.shop_url,
              currentPrice: price.price,
              originalPrice: price.original_price || price.price,
              discountPercent: discountPercent,
              currency: price.currency,
              lastUpdated: price.last_updated
            });
          }
        }
      }
      
      // Sort by discount percentage and return top deals
      return deals
        .filter((deal, index, self) => self.findIndex(d => d.gameId === deal.gameId) === index) // Remove duplicates
        .sort((a, b) => b.discountPercent - a.discountPercent)
        .slice(0, limit);
        
    } catch (error) {
      console.error('Error getting best deals:', error);
      return [];
    }
  }

  /**
   * Get lowest price for a game
   */
  async getLowestPrice(gameId: string): Promise<PriceData | null> {
    try {
      const { data: prices, error } = await supabase
        .from('price_tracking')
        .select('*')
        .eq('game_id', gameId)
        .order('price', { ascending: true })
        .limit(1);

      if (error || !prices || prices.length === 0) return null;

      const price = prices[0];
      return {
        gameId: price.game_id,
        shopName: price.shop_name,
        shopUrl: price.shop_url,
        price: price.price,
        originalPrice: price.original_price,
        currency: price.currency,
        isOnSale: price.is_on_sale,
        discountPercent: price.discount_percent,
        lastUpdated: price.last_updated
      };
    } catch (error) {
      console.error('Error getting lowest price:', error);
      return null;
    }
  }
}

// Export API service instances
export const priceTrackingDatabaseAPI = new PriceTrackingDatabaseAPI();
export const priceTrackingService = new PriceTrackingService();

// Keep backward compatibility
export const priceTracking = priceTrackingDatabaseAPI;