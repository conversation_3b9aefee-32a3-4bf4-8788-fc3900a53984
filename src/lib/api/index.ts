// Games API
export {
  IGD<PERSON>PIService,
  TheGamesDBAPIService,
  igdbAPI,
  theGamesDBAPI,
  gameAPI,
  type IGDBGame,
  type IGDBSearchOptions,
  type TheGamesDBGame,
  type TheGamesDBSearchOptions
} from './games';

// SteamGridDB API
export {
  SteamGridDBAPIService,
  steamGridDBAPI,
  type SteamGridDBGame,
  type SteamGridDBGrid,
  type SteamGridDBSearchOptions,
  type SteamGridDBGridOptions
} from './steamgriddb';

// RAWG API
export {
  RAWGAPIService,
  rawgAPI,
  type RAWGGame,
  type RAWGSearchOptions
} from './rawg';

// Users API
export {
  UserAuthAPI,
  UserProfileAPI,
  UserPreferencesAPI,
  UserRecommendationsAPI,
  userAuthAPI,
  userProfileAPI,
  userPreferencesAPI,
  userRecommendationsAPI,
  auth
} from './users';

// Collections API
export {
  UserGamesAPI,
  PriceTrackingAPI,
  SmartCollectionsAPI,
  userGamesAPI,
  priceTrackingAPI,
  smartCollectionsAPI,
  userGames,
  priceTracking
} from './collections';

// Recommendations API
export {
  AIRecommendationService,
  UserRecommendationsAPI,
  aiRecommendationService,
  userRecommendationsAPI,
  recommendations,
  type GameRecommendation,
  type UserGameProfile,
  type RecommendationRequest
} from './recommendations';

// Pricing API
export {
  PriceTrackingDatabaseAPI,
  PriceTrackingService,
  priceTrackingDatabaseAPI,
  priceTrackingService,
  type PriceData,
  type DealInfo,
  type PriceAlert
} from './pricing';

// Consolidated API object for backward compatibility
export const api = {
  // Games
  games: {
    igdb: igdbAPI,
    theGamesDB: theGamesDBAPI,
    steamGridDB: steamGridDBAPI,
    rawg: rawgAPI,
    search: gameAPI.search.bind(gameAPI),
    getDetails: gameAPI.getGameDetails.bind(gameAPI),
    getPopular: gameAPI.getPopularGames.bind(gameAPI),
    getNewReleases: gameAPI.getNewReleases.bind(gameAPI)
  },
  
  // Users
  auth: userAuthAPI,
  users: {
    profile: userProfileAPI,
    preferences: userPreferencesAPI,
    recommendations: userRecommendationsAPI
  },
  
  // Collections
  collections: {
    userGames: userGamesAPI,
    smartCollections: smartCollectionsAPI
  },
  
  // Pricing
  pricing: {
    database: priceTrackingDatabaseAPI,
    service: priceTrackingService
  },
  
  // Recommendations
  recommendations: {
    ai: aiRecommendationService,
    database: userRecommendationsAPI
  }
};

// Export individual APIs for direct access
export {
  igdbAPI,
  theGamesDBAPI,
  steamGridDBAPI,
  rawgAPI,
  gameAPI,
  userAuthAPI,
  userProfileAPI,
  userPreferencesAPI,
  userRecommendationsAPI,
  userGamesAPI,
  priceTrackingAPI,
  smartCollectionsAPI,
  aiRecommendationService,
  priceTrackingDatabaseAPI,
  priceTrackingService
};