import { 
  <PERSON><PERSON><PERSON><PERSON><PERSON>, 
  RAWGSearchResponse, 
  RAWGSearchOptions, 
  Platform, 
  Game 
} from '../../types';

/**
 * RAWG API Service for comprehensive game data
 * Provides access to one of the largest game databases with detailed metadata
 */
export class RAWGAPIService {
  private readonly baseUrl = 'https://api.rawg.io/api';
  private readonly apiKey: string | null;

  // Platform mapping from RAWG platform IDs to our Platform type
  private readonly platformMap: Record<number, Platform> = {
    4: 'PC',
    187: 'PlayStation 5',
    18: 'PlayStation 4',
    16: 'PlayStation 3',
    15: 'PlayStation 2',
    27: 'PlayStation',
    19: 'PlayStation Vita',
    186: 'Xbox Series X/S',
    1: 'Xbox One',
    14: 'Xbox 360',
    80: 'Xbox',
    7: 'Nintendo Switch',
    8: 'Nintendo 3DS',
    9: 'Nintendo DS',
    10: 'Wii U',
    11: 'Wii',
    105: 'GameCube',
    83: 'Nintendo 64',
    3: 'iOS',
    21: 'Android',
    171: 'Steam Deck',
    5: 'Mac',
    6: 'Linux',
    25: '3DO',
    23: 'Arcade',
    107: 'Sega Saturn'
  };

  // Parent platform mapping for broader platform categories
  private readonly parentPlatformMap: Record<number, Platform[]> = {
    1: ['PC', 'Mac', 'Linux'], // PC
    2: ['PlayStation 5', 'PlayStation 4', 'PlayStation 3', 'PlayStation 2', 'PlayStation', 'PlayStation Vita'], // PlayStation
    3: ['Xbox Series X/S', 'Xbox One', 'Xbox 360', 'Xbox'], // Xbox
    4: ['iOS'], // iOS
    8: ['Android'], // Android
    5: ['Mac'], // Apple Macintosh
    6: ['Linux'], // Linux
    7: ['Nintendo Switch', 'Nintendo 3DS', 'Nintendo DS', 'Wii U', 'Wii', 'GameCube', 'Nintendo 64'], // Nintendo
    9: ['Arcade'], // Arcade
    10: ['Sega Saturn'], // Sega
    11: ['3DO'] // 3DO
  };

  constructor() {
    this.apiKey = import.meta.env.VITE_RAWG_API_KEY;
    console.log('🎮 RAWG API Service initialized');
  }

  isConfigured(): boolean {
    return !!(this.apiKey && this.apiKey !== 'your_rawg_api_key');
  }

  /**
   * Make authenticated request to RAWG API
   */
  private async makeRequest<T>(endpoint: string, params: Record<string, unknown> = {}): Promise<T> {
    if (!this.isConfigured()) {
      throw new Error('RAWG API key not configured');
    }

    const url = new URL(`${this.baseUrl}/${endpoint}`);
    url.searchParams.append('key', this.apiKey!);
    
    // Add all parameters to URL
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        url.searchParams.append(key, String(value));
      }
    });

    console.log(`🔍 RAWG API request: ${endpoint}`, params);

    try {
      const response = await fetch(url.toString(), {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'Codexa/1.0'
        }
      });

      if (!response.ok) {
        throw new Error(`RAWG API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log(`✅ RAWG request successful - ${Array.isArray(data.results) ? data.results.length : 1} results`);
      return data;
    } catch (error) {
      console.error('❌ RAWG API error:', error);
      throw error;
    }
  }

  /**
   * Search games by name
   */
  async searchByName(query: string, options: RAWGSearchOptions = {}): Promise<RAWGGame[]> {
    if (!this.isConfigured()) {
      console.warn('⚠️ RAWG API not configured');
      return [];
    }

    const searchOptions: RAWGSearchOptions = {
      search: query,
      page_size: options.page_size || 40,
      exclude_additions: true, // Exclude DLC by default
      exclude_parents: false,
      exclude_game_series: false,
      ordering: '-relevance,-rating,-released',
      ...options
    };

    try {
      const response = await this.makeRequest<RAWGSearchResponse>('games', searchOptions);
      return response.results || [];
    } catch (error) {
      console.error('❌ RAWG search error:', error);
      return [];
    }
  }

  /**
   * Search games by platform
   */
  async searchByPlatform(platform: Platform, searchTerm: string = '', options: RAWGSearchOptions = {}): Promise<RAWGGame[]> {
    if (!this.isConfigured()) {
      console.warn('⚠️ RAWG API not configured');
      return [];
    }

    // Find platform ID for the given platform
    const platformId = Object.entries(this.platformMap).find(([, mappedPlatform]) => 
      mappedPlatform === platform
    )?.[0];

    if (!platformId) {
      console.warn(`⚠️ Platform ID not found for: ${platform}`);
      return [];
    }

    console.log(`🎮 RAWG searching platform "${platform}" (ID: ${platformId}) with term: "${searchTerm}"`);

    const searchOptions: RAWGSearchOptions = {
      search: searchTerm || undefined,
      platforms: platformId,
      page_size: options.page_size || 40,
      exclude_additions: true,
      ordering: '-relevance,-rating,-released',
      ...options
    };

    try {
      const response = await this.makeRequest<RAWGSearchResponse>('games', searchOptions);
      return response.results || [];
    } catch (error) {
      console.error('❌ RAWG platform search error:', error);
      return [];
    }
  }

  /**
   * Get game details by ID
   */
  async getGameDetails(gameId: number): Promise<RAWGGame | null> {
    if (!this.isConfigured()) {
      console.warn('⚠️ RAWG API not configured');
      return null;
    }

    try {
      const game = await this.makeRequest<RAWGGame>(`games/${gameId}`);
      return game;
    } catch (error) {
      console.error('❌ RAWG game details error:', error);
      return null;
    }
  }

  /**
   * Convert RAWG game to our standard Game interface
   */
  convertToGame(rawgGame: RAWGGame): Game {
    const platforms = rawgGame.platforms?.map(p => 
      this.platformMap[p.platform.id] || p.platform.name
    ).filter(Boolean) || [];

    const genres = rawgGame.genres?.map(g => g.name) || [];
    
    // Extract developer and publisher from stores/tags if available
    const developer = rawgGame.tags?.find(tag => 
      tag.name.toLowerCase().includes('developer') || 
      tag.name.toLowerCase().includes('studio')
    )?.name;

    const publisher = rawgGame.stores?.[0]?.store?.name;

    return {
      id: `rawg_${rawgGame.id}`,
      title: rawgGame.name,
      platforms,
      genres,
      developer,
      publisher,
      release_date: rawgGame.released,
      description: undefined, // RAWG doesn't provide description in search results
      cover_image: rawgGame.background_image,
      screenshots: rawgGame.short_screenshots?.map(s => s.image) || [],
      youtube_links: [],
      metacritic_score: rawgGame.metacritic,
      igdb_id: undefined
    };
  }

  /**
   * Get popular games
   */
  async getPopularGames(options: RAWGSearchOptions = {}): Promise<RAWGGame[]> {
    if (!this.isConfigured()) {
      console.warn('⚠️ RAWG API not configured');
      return [];
    }

    const searchOptions: RAWGSearchOptions = {
      ordering: '-rating,-added',
      page_size: options.page_size || 20,
      exclude_additions: true,
      ...options
    };

    try {
      const response = await this.makeRequest<RAWGSearchResponse>('games', searchOptions);
      return response.results || [];
    } catch (error) {
      console.error('❌ RAWG popular games error:', error);
      return [];
    }
  }

  /**
   * Get new releases
   */
  async getNewReleases(options: RAWGSearchOptions = {}): Promise<RAWGGame[]> {
    if (!this.isConfigured()) {
      console.warn('⚠️ RAWG API not configured');
      return [];
    }

    const currentDate = new Date();
    const threeMonthsAgo = new Date(currentDate.getTime() - (90 * 24 * 60 * 60 * 1000));
    const dateRange = `${threeMonthsAgo.toISOString().split('T')[0]},${currentDate.toISOString().split('T')[0]}`;

    const searchOptions: RAWGSearchOptions = {
      dates: dateRange,
      ordering: '-released,-rating',
      page_size: options.page_size || 20,
      exclude_additions: true,
      ...options
    };

    try {
      const response = await this.makeRequest<RAWGSearchResponse>('games', searchOptions);
      return response.results || [];
    } catch (error) {
      console.error('❌ RAWG new releases error:', error);
      return [];
    }
  }
}

// Export singleton instance
export const rawgAPI = new RAWGAPIService();
