import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@/contexts/AuthContext';
import { db } from '@/lib/supabase';

export interface UserPreferences {
  theme: 'light' | 'dark' | 'system';
  default_platform: string;
  default_view_mode: 'status' | 'platform' | 'platform-family';
  preferred_platforms: string[];
  show_completed_games: boolean;
  enable_notifications: boolean;
  auto_backup: boolean;
  onboarding_completed: boolean;
  auto_filter_platforms: boolean;
}

// Helper to convert from database format to UI format
function convertFromDatabasePreferences(dbPrefs: Record<string, unknown>): UserPreferences {
  // Handle migration from old default_sort to new view mode system
  let defaultViewMode: 'status' | 'platform' | 'platform-family' = 'status';
  const defaultSort = dbPrefs?.display?.default_sort;
  
  if (defaultSort === 'platform') {
    defaultViewMode = 'platform';
  } else if (defaultSort === 'platform-family') {
    defaultViewMode = 'platform-family';
  } else if (dbPrefs?.display?.default_view_mode) {
    defaultViewMode = dbPrefs.display.default_view_mode;
  }

  return {
    theme: dbPrefs?.theme || 'system',
    default_platform: dbPrefs?.display?.default_sort || 'status',
    default_view_mode: defaultViewMode,
    preferred_platforms: [], // Not stored in current schema
    show_completed_games: dbPrefs?.display?.show_metacritic ?? true,
    enable_notifications: dbPrefs?.notifications?.price_alerts ?? false,
    auto_backup: false, // Not in current schema
    onboarding_completed: dbPrefs?.onboarding_completed ?? false,
    auto_filter_platforms: false, // Not in current schema
  };
}

// Helper to convert from UI format to database format
function convertToDatabasePreferences(uiPrefs: UserPreferences): Record<string, unknown> {
  return {
    theme: uiPrefs.theme,
    notifications: {
      price_alerts: uiPrefs.enable_notifications,
      new_deals: false,
      game_updates: false,
      newsletter: false
    },
    privacy: {
      profile_public: false,
      library_public: false,
      activity_public: false
    },
    display: {
      games_per_page: 20,
      default_sort: uiPrefs.default_platform,
      default_view_mode: uiPrefs.default_view_mode,
      show_metacritic: uiPrefs.show_completed_games,
      show_screenshots: true
    }
  };
}

const defaultPreferences: UserPreferences = {
  theme: 'system',
  default_platform: 'status',
  default_view_mode: 'status',
  preferred_platforms: [],
  show_completed_games: true,
  enable_notifications: false,
  auto_backup: false,
  onboarding_completed: false,
  auto_filter_platforms: false,
};

export function useUserPreferences() {
  const { user } = useAuth();
  const queryClient = useQueryClient();

  const { data: preferences, isLoading, error } = useQuery({
    queryKey: ['user-preferences', user?.id],
    queryFn: async () => {
      if (!user?.id) throw new Error('User not authenticated');
      
      try {
        const { data, error } = await db.userPreferences.getPreferences(user.id);
        if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
          console.warn('Failed to fetch user preferences:', error);
          return defaultPreferences;
        }
        
        // Convert from database format to UI format
        return data ? convertFromDatabasePreferences(data) : defaultPreferences;
      } catch (error) {
        console.warn('Error fetching user preferences:', error);
        return defaultPreferences;
      }
    },
    enabled: !!user?.id,
  });

  const updatePreferences = useMutation({
    mutationFn: async (newPreferences: UserPreferences) => {
      if (!user?.id) throw new Error('User not authenticated');
      
      const dbPreferences = convertToDatabasePreferences(newPreferences);
      
      try {
        // First, try to get existing preferences
        const { data: existing, error: getError } = await db.userPreferences.getPreferences(user.id);
        
        if (getError && getError.code !== 'PGRST116') {
          throw getError;
        }
        
        if (existing) {
          // Update existing preferences
          const { data, error } = await db.userPreferences.updatePreferences(user.id, dbPreferences);
          if (error) throw error;
          return data;
        } else {
          // Create new preferences
          const { data, error } = await db.userPreferences.createPreferences({
            user_id: user.id,
            ...dbPreferences
          });
          if (error) throw error;
          return data;
        }
      } catch (error) {
        console.error('Error updating preferences:', error);
        throw error;
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['user-preferences', user?.id] });
    }
  });

  return {
    preferences: preferences || defaultPreferences,
    isLoading,
    error,
    hasPreferences: !!preferences,
    updatePreferences: updatePreferences.mutate,
    isUpdating: updatePreferences.isPending,
  };
}

// Export the converter functions for use in Settings page
export { convertFromDatabasePreferences, convertToDatabasePreferences };