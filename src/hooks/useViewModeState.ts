import { useState, useEffect, useCallback } from 'react';
import { ViewMode } from '@/pages/Library/types';
import { PlatformFamily } from '@/lib/utils/platformFamilyUtils';
import { useUserPreferences } from './useUserPreferences';
import { 
  migrateLocalStorageViewPreferences,
  getDefaultViewModeFromLegacy,
  validateMigratedViewMode 
} from '@/lib/utils/viewModePreferenceMigration';

interface ViewModeStateHook {
  viewMode: ViewMode;
  collapsedPlatforms: Set<string>;
  collapsedFamilies: Set<PlatformFamily>;
  setViewMode: (mode: ViewMode) => void;
  togglePlatform: (platform: string) => void;
  toggleFamily: (family: PlatformFamily) => void;
  isLoading: boolean;
}

const STORAGE_KEYS = {
  VIEW_MODE: 'library-view-mode',
  COLLAPSED_PLATFORMS: 'library-collapsed-platforms',
  COLLAPSED_FAMILIES: 'library-collapsed-families',
} as const;

/**
 * Custom hook for managing view mode state with persistence
 * Handles localStorage and user preferences integration
 */
export function useViewModeState(): ViewModeStateHook {
  const { preferences, updatePreferences, isLoading: preferencesLoading } = useUserPreferences();
  
  const [viewMode, setViewModeState] = useState<ViewMode>('status');
  const [collapsedPlatforms, setCollapsedPlatforms] = useState<Set<string>>(new Set());
  const [collapsedFamilies, setCollapsedFamilies] = useState<Set<PlatformFamily>>(new Set());
  const [isInitialized, setIsInitialized] = useState(false);

  // Initialize state from localStorage and user preferences
  useEffect(() => {
    if (preferencesLoading || isInitialized) return;

    // Run localStorage migration first
    migrateLocalStorageViewPreferences();

    // Load view mode with migration logic
    const savedViewMode = localStorage.getItem(STORAGE_KEYS.VIEW_MODE) as ViewMode;
    let initialViewMode: ViewMode = 'status';

    if (savedViewMode && ['status', 'platform', 'platform-family'].includes(savedViewMode)) {
      // Use localStorage value if valid
      initialViewMode = validateMigratedViewMode(savedViewMode);
    } else if (preferences.default_view_mode) {
      // Use user preferences
      initialViewMode = validateMigratedViewMode(preferences.default_view_mode);
    } else {
      // Migration: get default from legacy preferences
      initialViewMode = getDefaultViewModeFromLegacy(preferences);
    }

    setViewModeState(initialViewMode);

    // Load collapsed platforms
    const savedCollapsedPlatforms = localStorage.getItem(STORAGE_KEYS.COLLAPSED_PLATFORMS);
    if (savedCollapsedPlatforms) {
      try {
        const parsed = JSON.parse(savedCollapsedPlatforms);
        if (Array.isArray(parsed)) {
          setCollapsedPlatforms(new Set(parsed));
        }
      } catch (error) {
        console.warn('Failed to parse saved collapsed platforms:', error);
      }
    }

    // Load collapsed families
    const savedCollapsedFamilies = localStorage.getItem(STORAGE_KEYS.COLLAPSED_FAMILIES);
    if (savedCollapsedFamilies) {
      try {
        const parsed = JSON.parse(savedCollapsedFamilies);
        if (Array.isArray(parsed)) {
          setCollapsedFamilies(new Set(parsed));
        }
      } catch (error) {
        console.warn('Failed to parse saved collapsed families:', error);
      }
    }

    setIsInitialized(true);
  }, [preferences, preferencesLoading, isInitialized]);

  // Save view mode to localStorage and user preferences
  const setViewMode = useCallback((mode: ViewMode) => {
    setViewModeState(mode);
    
    // Save to localStorage immediately for fast access
    localStorage.setItem(STORAGE_KEYS.VIEW_MODE, mode);
    
    // Update user preferences asynchronously
    const updatedPreferences = {
      ...preferences,
      default_view_mode: mode,
    };
    updatePreferences(updatedPreferences);
  }, [preferences, updatePreferences]);

  // Toggle platform collapse state
  const togglePlatform = useCallback((platform: string) => {
    setCollapsedPlatforms(prev => {
      const newSet = new Set(prev);
      if (newSet.has(platform)) {
        newSet.delete(platform);
      } else {
        newSet.add(platform);
      }
      
      // Save to localStorage
      localStorage.setItem(STORAGE_KEYS.COLLAPSED_PLATFORMS, JSON.stringify(Array.from(newSet)));
      
      return newSet;
    });
  }, []);

  // Toggle family collapse state
  const toggleFamily = useCallback((family: PlatformFamily) => {
    setCollapsedFamilies(prev => {
      const newSet = new Set(prev);
      if (newSet.has(family)) {
        newSet.delete(family);
      } else {
        newSet.add(family);
      }
      
      // Save to localStorage
      localStorage.setItem(STORAGE_KEYS.COLLAPSED_FAMILIES, JSON.stringify(Array.from(newSet)));
      
      return newSet;
    });
  }, []);

  return {
    viewMode,
    collapsedPlatforms,
    collapsedFamilies,
    setViewMode,
    togglePlatform,
    toggleFamily,
    isLoading: preferencesLoading || !isInitialized,
  };
}