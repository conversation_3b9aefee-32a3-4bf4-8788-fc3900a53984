/**
 * React hook for smart image display with dynamic aspect ratios and object fitting
 * Based on the game-card-image-improvements.md specifications
 */

import { useState, useEffect, useCallback, useMemo } from 'react';
import { 
  getImageDisplayConfig, 
  getImageDisplayClasses, 
  getImageDimensions,
  ImageDimensions,
  ImageDisplayConfig,
  ImageDisplayMode
} from '@/lib/utils/imageDisplayUtils';

export interface UseSmartImageDisplayOptions {
  src?: string | null;
  mode?: ImageDisplayMode;
  enableHover?: boolean;
  fallbackAspectRatio?: string;
  containerDimensions?: ImageDimensions;
}

export interface UseSmartImageDisplayReturn {
  containerClasses: string;
  imageClasses: string;
  config: ImageDisplayConfig;
  isLoading: boolean;
  hasError: boolean;
  imageDimensions: ImageDimensions | null;
  handleImageLoad: (event: React.SyntheticEvent<HTMLImageElement>) => void;
  handleImageError: () => void;
  retry: () => void;
}

/**
 * Hook for smart image display with automatic aspect ratio detection and object fitting
 */
export function useSmartImageDisplay({
  src,
  mode = 'adaptive',
  enableHover = true,
  fallbackAspectRatio = 'aspect-[2/3]',
  containerDimensions
}: UseSmartImageDisplayOptions): UseSmartImageDisplayReturn {
  const [isLoading, setIsLoading] = useState(!!src);
  const [hasError, setHasError] = useState(false);
  const [imageDimensions, setImageDimensions] = useState<ImageDimensions | null>(null);
  const [retryCount, setRetryCount] = useState(0);

  // Generate image display configuration
  const config = useMemo(() => {
    const displayConfig = getImageDisplayConfig(imageDimensions, containerDimensions || null, mode);
    
    // Use fallback aspect ratio if no dimensions are available
    if (!imageDimensions && fallbackAspectRatio) {
      return {
        ...displayConfig,
        aspectRatio: fallbackAspectRatio as number
      };
    }
    
    return displayConfig;
  }, [imageDimensions, containerDimensions, mode, fallbackAspectRatio]);

  // Generate CSS classes
  const { containerClasses, imageClasses } = useMemo(() => {
    return getImageDisplayClasses(config, enableHover);
  }, [config, enableHover]);

  // Load image dimensions when src changes
  useEffect(() => {
    if (!src) {
      setImageDimensions(null);
      setIsLoading(false);
      setHasError(false);
      return;
    }

    setIsLoading(true);
    setHasError(false);

    // Try to get image dimensions
    getImageDimensions(src)
      .then((dimensions) => {
        setImageDimensions(dimensions);
        setIsLoading(false);
      })
      .catch(() => {
        // If we can't get dimensions, we'll still try to display the image
        // The image load handler will handle the final loading state
        setImageDimensions(null);
      });
  }, [src, retryCount]);

  const handleImageLoad = useCallback((event: React.SyntheticEvent<HTMLImageElement>) => {
    const img = event.currentTarget;
    
    // Update dimensions if we don't have them yet
    if (!imageDimensions && img.naturalWidth && img.naturalHeight) {
      setImageDimensions({
        width: img.naturalWidth,
        height: img.naturalHeight
      });
    }
    
    setIsLoading(false);
    setHasError(false);
  }, [imageDimensions]);

  const handleImageError = useCallback(() => {
    setIsLoading(false);
    setHasError(true);
  }, []);

  const retry = useCallback(() => {
    if (src && retryCount < 2) {
      setRetryCount(prev => prev + 1);
      setIsLoading(true);
      setHasError(false);
      setImageDimensions(null);
    }
  }, [src, retryCount]);

  // Reset state when src changes
  useEffect(() => {
    setRetryCount(0);
  }, [src]);

  return {
    containerClasses,
    imageClasses,
    config,
    isLoading,
    hasError,
    imageDimensions,
    handleImageLoad,
    handleImageError,
    retry
  };
}

/**
 * Simplified hook for Steam-style image display
 */
export function useSteamImageDisplay(src?: string | null, enableHover: boolean = true) {
  return useSmartImageDisplay({
    src,
    mode: 'steam',
    enableHover,
    fallbackAspectRatio: 'aspect-[460/215]' // Steam capsule ratio
  });
}

/**
 * Simplified hook for flexible image display (prioritizes showing full image)
 */
export function useFlexibleImageDisplay(src?: string | null, enableHover: boolean = true) {
  return useSmartImageDisplay({
    src,
    mode: 'flexible',
    enableHover,
    fallbackAspectRatio: 'aspect-[2/3]' // Standard game cover ratio
  });
}

/**
 * Hook for traditional game card image display with improvements
 */
export function useGameCardImageDisplay(src?: string | null, enableHover: boolean = true) {
  return useSmartImageDisplay({
    src,
    mode: 'adaptive',
    enableHover,
    fallbackAspectRatio: 'aspect-[2/3]' // Standard game cover ratio
  });
}

export default useSmartImageDisplay;
