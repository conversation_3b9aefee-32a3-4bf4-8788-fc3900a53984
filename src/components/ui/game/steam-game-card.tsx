import { memo, useCallback, useState, useRef, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/base/card';
import { Badge } from '@/components/ui/base/badge';
import { But<PERSON> } from '@/components/ui/base/button';
import { DropdownMenu, DropdownMenuItem } from '@/components/ui/base/dropdown-menu';
import { 
  Play, 
  CheckCircle, 
  Pause, 
  Gamepad2,
  Calendar,
  MoreVertical,
  Clock,
  Heart,
  Eye,
  ThumbsUp,
  TrendingDown,
  Award,
  Monitor,
  Smartphone,
  Gamepad
} from 'lucide-react';
import { UserGameWithDetails } from '@/types/database';
import { GameCardInteractions, AnimationConfig } from '@/types/library';
import { useCustomArtwork } from '@/hooks/useCustomArtwork';
import { FlexibleGameImage } from '@/components/ui/game/enhanced-game-image';
import { cn } from '@/lib/utils';

// Card variant types
export type SteamCardVariant = 'grid' | 'list' | 'hero';
export type SteamInteractionMode = 'click' | 'hover' | 'focus';

interface SteamGameCardProps {
  gameData: UserGameWithDetails;
  variant?: SteamCardVariant;
  interactionMode?: SteamInteractionMode;
  showQuickActions?: boolean;
  enableHoverEffects?: boolean;
  customArtwork?: boolean;
  animations?: Partial<AnimationConfig>;
  interactions?: GameCardInteractions;
  isSelected?: boolean;
  className?: string;
  showPricing?: boolean;
  showReviews?: boolean;
  showPlatforms?: boolean;
}

const defaultAnimations: AnimationConfig = {
  hoverScale: 1.02,
  transitionDuration: 300,
  staggerDelay: 100,
  parallaxEffect: false, // Steam cards are more subtle
  glowEffect: true,
};

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'playing':
      return <Play className="h-3 w-3 text-green-400" />;
    case 'completed':
      return <CheckCircle className="h-3 w-3 text-blue-400" />;
    case 'backlog':
      return <Pause className="h-3 w-3 text-orange-400" />;
    case 'wishlist':
      return <Heart className="h-3 w-3 text-pink-400" />;
    default:
      return <Gamepad2 className="h-3 w-3 text-gray-400" />;
  }
};

const getPlatformIcons = (platforms?: string[]) => {
  if (!platforms || platforms.length === 0) return null;
  
  const iconMap = {
    'PC': <Monitor className="h-3 w-3" />,
    'Windows': <Monitor className="h-3 w-3" />,
    'Mac': <Monitor className="h-3 w-3" />,
    'Linux': <Monitor className="h-3 w-3" />,
    'Steam Deck': <Gamepad className="h-3 w-3" />,
    'Mobile': <Smartphone className="h-3 w-3" />,
    'Android': <Smartphone className="h-3 w-3" />,
    'iOS': <Smartphone className="h-3 w-3" />,
  };
  
  return platforms.slice(0, 3).map((platform, index) => (
    <span key={index} className="text-muted-foreground/80" title={platform}>
      {iconMap[platform as keyof typeof iconMap] || <Monitor className="h-3 w-3" />}
    </span>
  ));
};

const getReviewScore = (score?: number) => {
  if (!score) return null;
  
  const percentage = Math.round(score * 10); // Convert to percentage
  const isPositive = percentage >= 70;
  
  return (
    <div className="flex items-center gap-1">
      <ThumbsUp className={cn(
        "h-3 w-3",
        isPositive ? "text-blue-400" : "text-orange-400"
      )} />
      <span className={cn(
        "text-xs font-medium",
        isPositive ? "text-blue-400" : "text-orange-400"
      )}>
        {percentage}%
      </span>
    </div>
  );
};

export const SteamGameCard = memo<SteamGameCardProps>(({
  gameData,
  variant = 'grid',
  interactionMode = 'hover',
  showQuickActions = true,
  enableHoverEffects = true,
  customArtwork = true,
  animations = {},
  interactions = {},
  isSelected = false,
  className,
  showPricing = false,
  showReviews = true,
  showPlatforms = true,
}) => {
  const game = gameData.game;
  const [isHovered, setIsHovered] = useState(false);
  const cardRef = useRef<HTMLDivElement>(null);
  
  // Merge animations with defaults
  const animConfig = { ...defaultAnimations, ...animations };
  
  // Get custom artwork for this user game
  const { getBestCoverImage, hasCustomArtwork } = useCustomArtwork(gameData.id);
  
  // Steam-style interaction effects
  useEffect(() => {
    const card = cardRef.current;
    if (!card || !enableHoverEffects) return;
    
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === 'Enter' || e.key === ' ') {
        e.preventDefault();
        handleCardClick();
      }
    };
    
    card.addEventListener('keydown', handleKeyDown);
    return () => card.removeEventListener('keydown', handleKeyDown);
  }, [enableHoverEffects, handleCardClick]);

  const handleCardClick = useCallback(() => {
    if (interactionMode === 'click' && interactions.onSelect) {
      interactions.onSelect(!isSelected);
    }
  }, [interactionMode, interactions, isSelected]);

  const handleStatusChange = useCallback((status: string) => {
    interactions.onStatusChange?.(status);
  }, [interactions]);



  const handleMouseEnter = useCallback(() => {
    if (enableHoverEffects) {
      setIsHovered(true);
    }
  }, [enableHoverEffects]);

  const handleMouseLeave = useCallback(() => {
    if (enableHoverEffects) {
      setIsHovered(false);
    }
  }, [enableHoverEffects]);

  if (!game) return null;

  // Variant-specific layout and sizing
  const variantClasses = {
    grid: 'w-full max-w-[240px] flex-col',
    list: 'w-full flex-row min-h-[120px]',
    hero: 'w-full max-w-[400px] flex-col',
  };

  const imageAspect = {
    grid: 'aspect-[3/4]', // Taller portrait ratio like traditional game box art
    list: 'w-[184px] aspect-[3/4] flex-shrink-0',
    hero: 'aspect-[3/4]',
  };

  return (
    <Card 
      ref={cardRef}
      className={cn(
        'group cursor-pointer overflow-hidden relative bg-[#1e2328] border-[#3c4043] hover:bg-[#2a3037] transition-all duration-300',
        'hover:shadow-lg hover:shadow-blue-500/10 hover:border-blue-500/20',
        variantClasses[variant],
        isSelected && 'ring-1 ring-blue-400 shadow-blue-400/20',
        enableHoverEffects && 'hover:-translate-y-0.5',
        className
      )}
      onClick={handleCardClick}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      style={{
        transitionDuration: `${animConfig.transitionDuration}ms`,
      }}
    >
      <div className={cn(
        'flex',
        variant === 'list' ? 'flex-row' : 'flex-col'
      )}>
        {/* Enhanced Steam-style Image Container */}
        <div className={cn(
          'relative overflow-hidden',
          imageAspect[variant],
          variant === 'grid' || variant === 'hero' ? 'rounded-t-lg' : 'rounded-l-lg',
          'group-hover:shadow-inner transition-all duration-300'
        )}>
          <FlexibleGameImage
            src={getBestCoverImage() || game.cover_image}
            alt={game.title}
            gameName={game.title}
            enableHover={enableHoverEffects}
            showFallback={true}
            loading="lazy"
            quality="medium"
            className={cn(
              imageAspect[variant],
              variant === 'grid' || variant === 'hero' ? 'rounded-t-lg' : 'rounded-l-lg'
            )}
          />

          {/* Steam-style gradient overlay on hover */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-black/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

          {/* Subtle highlight effect */}
          <div className="absolute inset-0 bg-gradient-to-br from-blue-400/5 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

          {/* Steam-style corner elements */}
          {isHovered && (
            <div className="absolute top-2 right-2 flex gap-1">
              {customArtwork && hasCustomArtwork && (
                <Badge className="bg-blue-600/90 text-white border-0 text-xs px-1.5 py-0.5">
                  Custom
                </Badge>
              )}
            </div>
          )}

          {/* Status indicator overlay */}
          <div className="absolute bottom-2 left-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            <div className="flex items-center gap-1.5 px-2 py-1 bg-black/70 rounded-md backdrop-blur-sm">
              {getStatusIcon(gameData.status)}
              <span className="text-xs text-white/90 capitalize">{gameData.status}</span>
            </div>
          </div>

          {/* Quick actions for hero variant */}
          {variant === 'hero' && showQuickActions && isHovered && (
            <div className="absolute bottom-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <div className="flex gap-1">
                <Button
                  size="sm"
                  className="h-7 w-7 p-0 bg-blue-600/90 hover:bg-blue-500 border-0"
                  onClick={(e) => {
                    e.stopPropagation();
                    interactions.onQuickPlay?.();
                  }}
                >
                  <Play className="h-3.5 w-3.5" />
                </Button>
                <Button
                  size="sm"
                  className="h-7 w-7 p-0 bg-black/70 hover:bg-black/90 border-0"
                  onClick={(e) => {
                    e.stopPropagation();
                    interactions.onSelect?.(!isSelected);
                  }}
                >
                  <Heart className="h-3.5 w-3.5" />
                </Button>
              </div>
            </div>
          )}
        </div>

        {/* Steam-style Content Area - Title at bottom overlay */}
        <div className={cn(
          'relative',
          variant === 'list' ? 'flex-1 p-3' : 'p-0'
        )}>
          {variant !== 'list' && (
            // Grid/Hero: Title overlay at bottom of image
            <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/90 via-black/70 to-transparent p-3 z-10">
              <div className="space-y-1">
                <h3 className="text-white font-medium text-sm leading-tight line-clamp-2 group-hover:text-blue-300 transition-colors">
                  {game.title}
                </h3>
                
                {/* Steam-style metadata line */}
                <div className="flex items-center gap-2 text-xs">
                  {/* Review score */}
                  {showReviews && game.metacritic_score && getReviewScore(game.metacritic_score)}
                  
                  {/* Release year */}
                  {game.release_date && (
                    <span className="text-slate-300">
                      {new Date(game.release_date).getFullYear()}
                    </span>
                  )}
                  
                  {/* Platforms */}
                  {showPlatforms && (
                    <div className="flex items-center gap-1">
                      {getPlatformIcons(game.platforms)}
                    </div>
                  )}
                </div>
                
                {/* Enhanced pricing with Steam-style layout */}
                {showPricing && (
                  <div className="flex items-center gap-2">
                    {/* Price display with original/current pricing */}
                    {game.price_current ? (
                      <div className="flex items-center gap-2">
                        {game.price_original && game.price_original !== game.price_current && (
                          <>
                            <Badge className="bg-green-600 text-white text-xs px-1.5 py-0.5">
                              -{Math.round(((game.price_original - game.price_current) / game.price_original) * 100)}%
                            </Badge>
                            <span className="text-slate-400 line-through text-xs">
                              ${game.price_original.toFixed(2)}
                            </span>
                          </>
                        )}
                        <span className="text-green-400 font-medium text-sm">
                          ${game.price_current.toFixed(2)}
                        </span>
                      </div>
                    ) : (
                      <span className="text-green-400 font-medium text-sm">Free to Play</span>
                    )}
                    
                    {/* Steam sale indicator */}
                    {game.on_sale && (
                      <Badge className="bg-blue-600 text-white text-xs px-1.5 py-0.5">
                        <TrendingDown className="h-2.5 w-2.5 mr-0.5" />
                        Sale
                      </Badge>
                    )}
                  </div>
                )}
              </div>
            </div>
          )}

          {variant === 'list' && (
            // List: Traditional content area
            <CardContent className="p-0">
              <div className="space-y-2">
                <div>
                  <h3 className="text-white font-medium text-base leading-tight line-clamp-1 group-hover:text-blue-300 transition-colors">
                    {game.title}
                  </h3>
                  {game.developer && (
                    <p className="text-slate-400 text-sm">{game.developer}</p>
                  )}
                </div>

                {/* Steam-style metadata */}
                <div className="flex items-center gap-3 text-xs">
                  {/* Status */}
                  <div className="flex items-center gap-1">
                    {getStatusIcon(gameData.status)}
                    <span className="text-slate-300 capitalize">{gameData.status}</span>
                  </div>

                  {/* Review score */}
                  {showReviews && game.metacritic_score && getReviewScore(game.metacritic_score)}
                  
                  {/* Release year */}
                  {game.release_date && (
                    <span className="text-slate-400">
                      {new Date(game.release_date).getFullYear()}
                    </span>
                  )}
                </div>

                {/* Genres */}
                {game.genres && game.genres.length > 0 && (
                  <div className="flex flex-wrap gap-1">
                    {game.genres.slice(0, 3).map((genre, index) => (
                      <Badge 
                        key={index} 
                        variant="outline" 
                        className="text-xs border-slate-600 text-slate-300 bg-slate-800/50"
                      >
                        {genre}
                      </Badge>
                    ))}
                  </div>
                )}

                {/* Additional metadata for list view */}
                <div className="flex items-center justify-between text-xs text-slate-400">
                  <div className="flex items-center gap-3">
                    {gameData.hours_played && (
                      <div className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        <span>{Math.round(gameData.hours_played)}h</span>
                      </div>
                    )}
                    
                    {showPlatforms && (
                      <div className="flex items-center gap-1">
                        {getPlatformIcons(game.platforms)}
                      </div>
                    )}
                  </div>

                  {/* Quick actions for list */}
                  {showQuickActions && (
                    <div className="opacity-0 group-hover:opacity-100 transition-opacity">
                      <DropdownMenu
                        trigger={
                          <Button 
                            variant="ghost" 
                            size="sm" 
                            className="h-6 w-6 p-0 hover:bg-slate-700"
                            onClick={(e) => e.stopPropagation()}
                          >
                            <MoreVertical className="h-3 w-3" />
                          </Button>
                        }
                        align="end"
                      >
                        <DropdownMenuItem onClick={() => handleStatusChange('playing')}>
                          <Play className="h-4 w-4 mr-2" />
                          Mark as Playing
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleStatusChange('completed')}>
                          <CheckCircle className="h-4 w-4 mr-2" />
                          Mark as Completed
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleStatusChange('backlog')}>
                          <Pause className="h-4 w-4 mr-2" />
                          Move to Backlog
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleStatusChange('wishlist')}>
                          <Heart className="h-4 w-4 mr-2" />
                          Add to Wishlist
                        </DropdownMenuItem>
                      </DropdownMenu>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          )}
        </div>
      </div>

      {/* Steam-style hover overlay with rich content preview */}
      {isHovered && enableHoverEffects && variant !== 'list' && (
        <div className="absolute inset-0 bg-gradient-to-t from-black/95 via-black/70 to-black/20 opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none z-20">
          <div className="absolute bottom-0 left-0 right-0 p-4 space-y-3">
            {/* Enhanced hover content with Steam-like layout */}
            
            {/* Game description or developer info */}
            {game.developer && (
              <div className="text-xs text-slate-300">
                <span className="text-slate-400">Developer:</span> {game.developer}
              </div>
            )}
            
            {/* Genres with Steam-style tags */}
            {game.genres && game.genres.length > 0 && (
              <div className="flex flex-wrap gap-1">
                {game.genres.slice(0, 4).map((genre, index) => (
                  <Badge 
                    key={index} 
                    variant="outline" 
                    className="text-xs border-blue-500/30 text-blue-300 bg-blue-500/10 hover:bg-blue-500/20 transition-colors"
                  >
                    {genre}
                  </Badge>
                ))}
              </div>
            )}
            
            {/* Play time and achievements */}
            <div className="flex items-center justify-between text-xs">
              <div className="flex items-center gap-3">
                {gameData.hours_played && (
                  <div className="flex items-center gap-1 text-slate-300">
                    <Clock className="h-3 w-3" />
                    <span>{Math.round(gameData.hours_played)} hours played</span>
                  </div>
                )}
                
                {gameData.last_played && (
                  <div className="flex items-center gap-1 text-slate-400">
                    <Calendar className="h-3 w-3" />
                    <span>Last played {new Date(gameData.last_played).toLocaleDateString()}</span>
                  </div>
                )}
              </div>
              
              {/* Achievement indicator */}
              {gameData.achievements_unlocked && (
                <div className="flex items-center gap-1 text-yellow-400">
                  <Award className="h-3 w-3" />
                  <span>{gameData.achievements_unlocked} achievements</span>
                </div>
              )}
            </div>
            
            {/* Steam-style action buttons */}
            {showQuickActions && (
              <div className="flex items-center gap-2 pt-1">
                <Button
                  size="sm"
                  className="h-7 px-3 bg-green-600/90 hover:bg-green-500 text-white border-0 text-xs font-medium"
                  onClick={(e) => {
                    e.stopPropagation();
                    interactions.onQuickPlay?.();
                  }}
                >
                  <Play className="h-3 w-3 mr-1" />
                  Play
                </Button>
                <Button
                  size="sm"
                  className="h-7 px-3 bg-slate-700/90 hover:bg-slate-600 text-slate-200 border-slate-600 text-xs"
                  onClick={(e) => {
                    e.stopPropagation();
                    interactions.onViewDetails?.();
                  }}
                >
                  <Eye className="h-3 w-3 mr-1" />
                  Details
                </Button>
              </div>
            )}
          </div>
        </div>
      )}
    </Card>
  );
});

SteamGameCard.displayName = 'SteamGameCard';