/**
 * Steam-style Library Grid Component
 * Displays games in a Steam-inspired grid layout with hover effects
 */

import React, { useState, useCallback, useMemo } from 'react';
import { cn } from '@/lib/utils';
import { SteamLibraryCard } from './steam-library-card';
import { EnhancedGameDetailModal } from './enhanced-game-detail-modal';
import { UserGameWithDetails } from '@/types/game';
import { Button } from '@/components/ui/base/button';
import { Input } from '@/components/ui/base/input';
import { Badge } from '@/components/ui/base/badge';
import {
  Search,
  Grid3X3,
  List,
  ArrowUp as SortAsc,
  ArrowDown as SortDesc,
  Play,
  Clock,
  Star,
  Calendar
} from '@/lib/icons';

interface SteamLibraryGridProps {
  games: UserGameWithDetails[];
  onStatusUpdate?: (userGameId: string, status: string) => void;
  onQuickPlay?: (gameData: UserGameWithDetails) => void;
  onAddToWishlist?: (gameData: UserGameWithDetails) => void;
  className?: string;
  showFilters?: boolean;
  showSearch?: boolean;
  defaultView?: 'grid' | 'list';
}

type SortOption = 'name' | 'playtime' | 'rating' | 'recent' | 'status';
type FilterOption = 'all' | 'playing' | 'completed' | 'paused' | 'wishlist' | 'dropped';

export const SteamLibraryGrid: React.FC<SteamLibraryGridProps> = ({
  games,
  onStatusUpdate,
  onQuickPlay,
  onAddToWishlist,
  className,
  showFilters = true,
  showSearch = true,
  defaultView = 'grid'
}) => {
  const [selectedGame, setSelectedGame] = useState<UserGameWithDetails | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [sortBy, setSortBy] = useState<SortOption>('name');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc');
  const [filterBy, setFilterBy] = useState<FilterOption>('all');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>(defaultView);

  const handleGameClick = useCallback((gameData: UserGameWithDetails) => {
    setSelectedGame(gameData);
    setIsModalOpen(true);
  }, []);

  const handleCloseModal = useCallback(() => {
    setIsModalOpen(false);
    setSelectedGame(null);
  }, []);

  const handleQuickPlay = useCallback((gameData: UserGameWithDetails) => {
    onQuickPlay?.(gameData);
  }, [onQuickPlay]);

  const handleStatusUpdate = useCallback((userGameId: string, status: string) => {
    onStatusUpdate?.(userGameId, status);
  }, [onStatusUpdate]);

  const handleAddToWishlist = useCallback((gameData: UserGameWithDetails) => {
    onAddToWishlist?.(gameData);
  }, [onAddToWishlist]);

  // Filter and sort games
  const filteredAndSortedGames = useMemo(() => {
    let filtered = games;

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(gameData => 
        gameData.game.title.toLowerCase().includes(query) ||
        gameData.game.developer?.toLowerCase().includes(query) ||
        gameData.game.publisher?.toLowerCase().includes(query)
      );
    }

    // Apply status filter
    if (filterBy !== 'all') {
      filtered = filtered.filter(gameData => 
        gameData.status.toLowerCase() === filterBy
      );
    }

    // Apply sorting
    const sorted = [...filtered].sort((a, b) => {
      let comparison = 0;

      switch (sortBy) {
        case 'name':
          comparison = a.game.title.localeCompare(b.game.title);
          break;
        case 'playtime':
          comparison = (a.hours_played || 0) - (b.hours_played || 0);
          break;
        case 'rating':
          comparison = (a.personal_rating || 0) - (b.personal_rating || 0);
          break;
        case 'recent':
          comparison = new Date(a.updated_at || 0).getTime() - new Date(b.updated_at || 0).getTime();
          break;
        case 'status':
          comparison = a.status.localeCompare(b.status);
          break;
        default:
          comparison = 0;
      }

      return sortOrder === 'asc' ? comparison : -comparison;
    });

    return sorted;
  }, [games, searchQuery, filterBy, sortBy, sortOrder]);

  const toggleSort = useCallback((option: SortOption) => {
    if (sortBy === option) {
      setSortOrder(prev => prev === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(option);
      setSortOrder('asc');
    }
  }, [sortBy]);

  const getStatusCount = useCallback((status: FilterOption) => {
    if (status === 'all') return games.length;
    return games.filter(game => game.status.toLowerCase() === status).length;
  }, [games]);

  return (
    <div className={cn('space-y-6', className)}>
      {/* Header with search and filters */}
      {(showSearch || showFilters) && (
        <div className="space-y-4">
          {/* Search and view controls */}
          <div className="flex items-center gap-4">
            {showSearch && (
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder="Search your library..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 bg-gray-800 border-gray-700 text-white placeholder-gray-400"
                />
              </div>
            )}

            <div className="flex items-center gap-2">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'secondary'}
                size="sm"
                onClick={() => setViewMode('grid')}
                className="px-3"
              >
                <Grid3X3 className="h-4 w-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'secondary'}
                size="sm"
                onClick={() => setViewMode('list')}
                className="px-3"
              >
                <List className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Filters and sorting */}
          {showFilters && (
            <div className="flex flex-wrap items-center gap-4">
              {/* Status filters */}
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-400">Filter:</span>
                {[
                  { id: 'all', label: 'All Games' },
                  { id: 'playing', label: 'Playing' },
                  { id: 'completed', label: 'Completed' },
                  { id: 'paused', label: 'Paused' },
                  { id: 'wishlist', label: 'Wishlist' },
                  { id: 'dropped', label: 'Dropped' }
                ].map((filter) => (
                  <Button
                    key={filter.id}
                    variant={filterBy === filter.id ? 'default' : 'secondary'}
                    size="sm"
                    onClick={() => setFilterBy(filter.id as FilterOption)}
                    className="text-xs"
                  >
                    {filter.label}
                    <Badge 
                      variant="secondary" 
                      className="ml-2 text-xs px-1.5 py-0.5"
                    >
                      {getStatusCount(filter.id as FilterOption)}
                    </Badge>
                  </Button>
                ))}
              </div>

              {/* Sort options */}
              <div className="flex items-center gap-2 ml-auto">
                <span className="text-sm text-gray-400">Sort by:</span>
                {[
                  { id: 'name', label: 'Name', icon: SortAsc },
                  { id: 'playtime', label: 'Playtime', icon: Clock },
                  { id: 'rating', label: 'Rating', icon: Star },
                  { id: 'recent', label: 'Recent', icon: Calendar }
                ].map((sort) => {
                  const Icon = sort.icon;
                  const isActive = sortBy === sort.id;
                  return (
                    <Button
                      key={sort.id}
                      variant={isActive ? 'default' : 'secondary'}
                      size="sm"
                      onClick={() => toggleSort(sort.id as SortOption)}
                      className="text-xs"
                    >
                      <Icon className="h-3 w-3 mr-1" />
                      {sort.label}
                      {isActive && (
                        sortOrder === 'desc' ? 
                          <SortDesc className="h-3 w-3 ml-1" /> : 
                          <SortAsc className="h-3 w-3 ml-1" />
                      )}
                    </Button>
                  );
                })}
              </div>
            </div>
          )}
        </div>
      )}

      {/* Results summary */}
      <div className="flex items-center justify-between text-sm text-gray-400">
        <span>
          Showing {filteredAndSortedGames.length} of {games.length} games
        </span>
        {searchQuery && (
          <span>
            Search results for "{searchQuery}"
          </span>
        )}
      </div>

      {/* Games grid */}
      <div className={cn(
        'grid gap-4 animate-fade-in',
        viewMode === 'grid' 
          ? 'grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6'
          : 'grid-cols-1'
      )}>
        {filteredAndSortedGames.map((gameData, index) => (
          <div
            key={gameData.id}
            className="animate-slide-up"
            style={{ animationDelay: `${Math.min(index * 0.05, 1)}s` }}
          >
            <SteamLibraryCard
              gameData={gameData}
              onGameClick={handleGameClick}
              onStatusUpdate={handleStatusUpdate}
              onQuickPlay={handleQuickPlay}
              showHoverDetails={viewMode === 'grid'}
              enableQuickActions={true}
              showSteamHoverCard={viewMode === 'grid'}
            />
          </div>
        ))}
      </div>

      {/* Empty state */}
      {filteredAndSortedGames.length === 0 && (
        <div className="text-center py-12 text-gray-400">
          <Play className="h-12 w-12 mx-auto mb-4 opacity-50" />
          <h3 className="text-lg font-medium mb-2">No games found</h3>
          <p>
            {searchQuery 
              ? `No games match "${searchQuery}"`
              : filterBy !== 'all' 
                ? `No games with status "${filterBy}"`
                : 'Your library is empty'
            }
          </p>
        </div>
      )}

      {/* Enhanced Game Detail Modal */}
      <EnhancedGameDetailModal
        gameData={selectedGame}
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        onStatusUpdate={handleStatusUpdate}
        onQuickPlay={handleQuickPlay}
        onAddToWishlist={handleAddToWishlist}
      />
    </div>
  );
};
