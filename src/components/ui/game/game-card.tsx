import { memo, useCallback, useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/base/card';
import { Badge } from '@/components/ui/base/badge';
import { Button } from '@/components/ui/base/button';
import { DropdownMenu, DropdownMenuItem } from '@/components/ui/base/dropdown-menu';
import { UserGameWithDetails } from '@/types/database';
import { useCustomArtwork } from '@/hooks/useCustomArtwork';
import { useGameTags } from '@/hooks/useGameTags';
import { useUserTags } from '@/hooks/useUserTags';
import { getStatusIconElement, getStatusColor } from '@/lib/gameStatusUtils';
import { EnhancedGameImage } from '@/components/ui/game/enhanced-game-image';
import {
  Calendar,
  MoreVertical,
  Trash2,
  Upload,
  Play,
  CheckCircle,
  Pause,
  Star,
  Tag,
  Plus
} from '@/lib/icons';

interface GameCardProps {
  gameData: UserGameWithDetails;
  onGameClick: (gameData: UserGameWithDetails) => void;
  onUpdateStatus: (userGameId: string, status: string) => void;
  onRemoveGame: (userGameId: string) => void;
}

export const GameCard = memo<GameCardProps>(({ gameData, onGameClick, onUpdateStatus, onRemoveGame }) => {
  const game = gameData.game;
  const [showTagInput, setShowTagInput] = useState(false);

  // Get custom artwork for this user game
  const { getBestCoverImage, hasCustomArtwork } = useCustomArtwork(gameData.id);

  // Get tag functionality
  const { gameTags, addTag, removeTag } = useGameTags(gameData.id);
  const { data: userTags } = useUserTags();

  const handleGameClick = useCallback(() => {
    onGameClick(gameData);
  }, [gameData, onGameClick]);

  const handleUpdateStatus = useCallback((status: string) => {
    onUpdateStatus(gameData.id, status);
  }, [gameData.id, onUpdateStatus]);

  const handleRemoveGame = useCallback(() => {
    onRemoveGame(gameData.id);
  }, [gameData.id, onRemoveGame]);



  const handleQuickTag = useCallback(async (tagId: string) => {
    try {
      if (gameTags.some(tag => tag.id === tagId)) {
        removeTag(tagId);
      } else {
        addTag(tagId);
      }
    } catch (error) {
      console.error('Error toggling tag:', error);
    }
  }, [gameTags, addTag, removeTag]);

  const toggleTagInput = useCallback(() => {
    setShowTagInput(!showTagInput);
  }, [showTagInput]);

  if (!game) return null;

  return (
    <Card 
      className="group hover:shadow-2xl hover:shadow-primary/10 transition-premium cursor-pointer hover:scale-[1.02] hover:-translate-y-1 animate-stagger-fade overflow-hidden"
      onClick={handleGameClick}
    >
      <CardHeader className="pb-2">
        <div className="flex items-start justify-between">
          <div className="flex-1 min-w-0">
            <CardTitle className="text-lg truncate group-hover:text-primary transition-colors">
              {game.title}
            </CardTitle>
            <CardDescription className="flex items-center gap-2 mt-1">
              {getStatusIconElement(gameData.status)}
              <span className="capitalize">{gameData.status}</span>
              {game.developer && (
                <>
                  <span className="text-muted-foreground">•</span>
                  <span className="truncate">{game.developer}</span>
                </>
              )}
            </CardDescription>
          </div>
          
          <DropdownMenu
            trigger={
              <Button 
                variant="ghost" 
                size="sm" 
                className="opacity-0 group-hover:opacity-100 transition-opacity"
                onClick={(e) => e.stopPropagation()}
              >
                <MoreVertical className="h-4 w-4" />
              </Button>
            }
            align="end"
          >
            <DropdownMenuItem onClick={() => handleUpdateStatus('playing')}>
              <Play className="h-4 w-4 mr-2" />
              Mark as Playing
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleUpdateStatus('completed')}>
              <CheckCircle className="h-4 w-4 mr-2" />
              Mark as Completed
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleUpdateStatus('backlog')}>
              <Pause className="h-4 w-4 mr-2" />
              Move to Backlog
            </DropdownMenuItem>
            <DropdownMenuItem 
              onClick={handleRemoveGame}
              className="text-destructive focus:text-destructive"
            >
              <Trash2 className="h-4 w-4 mr-2" />
              Remove from Library
            </DropdownMenuItem>
          </DropdownMenu>
        </div>
      </CardHeader>

      <CardContent>
        <div className="space-y-3">
          {/* Enhanced Game Image with Smart Display */}
          <div className="relative">
            <EnhancedGameImage
              src={getBestCoverImage() || game.cover_image}
              alt={game.title}
              gameName={game.title}
              mode="adaptive"
              enableHover={true}
              showFallback={true}
              loading="lazy"
              quality="medium"
            />

            {/* Premium hover effects with responsive design */}
            <div className="absolute inset-0 bg-gradient-to-t from-background/80 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-premium" />
            <div className="absolute top-2 right-2 w-2 h-2 bg-primary rounded-full opacity-0 group-hover:opacity-100 animate-glow transition-premium" />

            {/* Status indicator for different screen sizes */}
            <div className="absolute bottom-2 left-2 opacity-0 group-hover:opacity-100 transition-premium">
              <div className="w-8 h-8 rounded-full bg-black/40 flex items-center justify-center">
                {getStatusIconElement(gameData.status)}
              </div>
            </div>

            {/* Custom artwork indicator */}
            {hasCustomArtwork && (
              <div className="absolute top-2 left-2 opacity-0 group-hover:opacity-100 transition-premium">
                <Badge variant="secondary" className="bg-primary text-primary-foreground border-0 text-xs">
                  <Upload className="h-3 w-3 mr-1" />
                  Custom
                </Badge>
              </div>
            )}
          </div>

          <div className="flex flex-wrap gap-2">
            <Badge className={getStatusColor(gameData.status)}>
              {gameData.status}
            </Badge>

            {game.metacritic_score && (
              <Badge variant="secondary">
                <Star className="h-3 w-3 mr-1" />
                {game.metacritic_score}
              </Badge>
            )}
          </div>

          {game.genres && game.genres.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {game.genres.slice(0, 3).map((genre, index) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {genre}
                </Badge>
              ))}
              {game.genres.length > 3 && (
                <Badge variant="outline" className="text-xs">
                  +{game.genres.length - 3} more
                </Badge>
              )}
            </div>
          )}

          {/* Game Tags Section */}
          {(gameTags.length > 0 || showTagInput) && (
            <div className="space-y-2">
              {gameTags.length > 0 && (
                <div className="flex flex-wrap gap-1">
                  {gameTags.slice(0, 3).map((tag) => (
                    <Badge 
                      key={tag.id} 
                      variant="secondary" 
                      className="text-xs cursor-pointer hover:bg-destructive hover:text-destructive-foreground transition-colors"
                      style={{ backgroundColor: `${tag.color}20`, borderColor: tag.color }}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleQuickTag(tag.id);
                      }}
                    >
                      <Tag className="h-3 w-3 mr-1" />
                      {tag.name}
                    </Badge>
                  ))}
                  {gameTags.length > 3 && (
                    <Badge variant="secondary" className="text-xs">
                      +{gameTags.length - 3} more
                    </Badge>
                  )}
                </div>
              )}
              
              {/* Quick Tag Button */}
              <div className="flex items-center gap-1">
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 px-2 text-xs"
                  onClick={(e) => {
                    e.stopPropagation();
                    toggleTagInput();
                  }}
                >
                  <Plus className="h-3 w-3 mr-1" />
                  Tag
                </Button>
                
                {/* Quick Popular Tags */}
                {userTags && userTags.slice(0, 2).map((tag) => {
                  const isApplied = gameTags.some(gt => gt.id === tag.id);
                  return (
                    <Badge
                      key={tag.id}
                      variant={isApplied ? "default" : "outline"}
                      className="text-xs cursor-pointer hover:scale-105 transition-transform"
                      style={isApplied ? { backgroundColor: tag.color, borderColor: tag.color } : { borderColor: tag.color }}
                      onClick={(e) => {
                        e.stopPropagation();
                        handleQuickTag(tag.id);
                      }}
                    >
                      {tag.name}
                    </Badge>
                  );
                })}
              </div>
            </div>
          )}

          {gameData.personal_rating && (
            <div className="flex items-center gap-1 text-sm text-muted-foreground">
              <Star className="h-4 w-4 fill-current text-yellow-500" />
              <span>Your rating: {gameData.personal_rating}/10</span>
            </div>
          )}

          {game.release_date && (
            <div className="flex items-center gap-1 text-sm text-muted-foreground">
              <Calendar className="h-4 w-4" />
              <span>{new Date(game.release_date).getFullYear()}</span>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
});

GameCard.displayName = 'GameCard';