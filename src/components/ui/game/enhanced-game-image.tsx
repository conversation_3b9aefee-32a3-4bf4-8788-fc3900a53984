/**
 * Enhanced Game Image Component with Smart Display
 * Implements the improvements from game-card-image-improvements.md
 */

import React, { useState, useCallback } from 'react';
import { cn } from '@/lib/utils';
import { ImageIcon } from '@/lib/icons';
import { useSmartImageDisplay } from '@/hooks/useSmartImageDisplay';
import { useImageWithFallback } from '@/hooks/useImageWithFallback';
import { ImageDisplayMode } from '@/lib/utils/imageDisplayUtils';

export interface EnhancedGameImageProps {
  src?: string | null;
  alt: string;
  gameName?: string;
  className?: string;
  mode?: ImageDisplayMode;
  enableHover?: boolean;
  showFallback?: boolean;

  onLoad?: () => void;
  onError?: () => void;
  quality?: 'low' | 'medium' | 'high';
}

/**
 * Enhanced Game Image with smart aspect ratios and object fitting
 */
export const EnhancedGameImage: React.FC<EnhancedGameImageProps> = ({
  src,
  alt,
  gameName,
  className,
  mode = 'adaptive',
  enableHover = true,
  showFallback = true,
  onLoad,
  onError,
  quality = 'medium'
}) => {
  const [imageLoaded, setImageLoaded] = useState(false);

  // Use smart image display hook
  const {
    containerClasses,
    imageClasses,
    isLoading,
    hasError,
    handleImageLoad,
    handleImageError,
    retry
  } = useSmartImageDisplay({
    src,
    mode,
    enableHover
  });

  // Use image fallback hook for placeholder generation
  const { imageSrc } = useImageWithFallback({
    src,
    fallbackText: alt,
    type: 'cover',
    gameName
  });

  const qualityProps = {
    low: { loading: 'lazy' as const, decoding: 'async' as const },
    medium: { loading: 'lazy' as const, decoding: 'async' as const },
    high: { loading: 'eager' as const, decoding: 'sync' as const }
  }[quality];

  const handleLoad = useCallback((event: React.SyntheticEvent<HTMLImageElement>) => {
    setImageLoaded(true);
    handleImageLoad(event);
    onLoad?.();
  }, [handleImageLoad, onLoad]);

  const handleError = useCallback(() => {
    setImageLoaded(true);
    handleImageError();
    onError?.();
  }, [handleImageError, onError]);

  return (
    <div className={cn(containerClasses, className)}>
      <div className="relative w-full h-full">
        {src && !hasError ? (
          <>
            {/* Loading shimmer effect */}
            {(isLoading || !imageLoaded) && (
              <div className="absolute inset-0 box-art-shimmer rounded-lg">
                <div className="w-full h-full bg-gradient-to-br from-muted/80 via-muted/60 to-muted/40 animate-pulse" />
              </div>
            )}
            
            {/* Main image */}
            <img
              src={imageSrc}
              alt={alt}
              className={cn(
                imageClasses,
                imageLoaded 
                  ? 'opacity-100' 
                  : 'opacity-0 scale-105'
              )}
              onLoad={handleLoad}
              onError={handleError}
              style={{
                imageRendering: 'crisp-edges',
              }}
              {...qualityProps}
            />

            {/* Retry button for failed images */}
            {hasError && (
              <div className="absolute inset-0 flex items-center justify-center bg-muted/80">
                <button
                  onClick={retry}
                  className="px-3 py-1 text-xs bg-primary text-primary-foreground rounded hover:bg-primary/90 transition-colors"
                >
                  Retry
                </button>
              </div>
            )}
          </>
        ) : showFallback ? (
          /* Fallback placeholder */
          <div className="w-full h-full flex flex-col items-center justify-center bg-gradient-to-br from-muted via-muted/90 to-muted/70 group-hover:from-muted/80 group-hover:to-muted/60 transition-premium">
            <div className="relative mb-3">
              <div className="absolute inset-0 bg-gradient-to-r from-primary/20 via-secondary/20 to-accent/20 rounded-full animate-glow" />
              <ImageIcon className="h-16 w-16 text-muted-foreground/60 animate-pulse relative z-10" />
              <div className="absolute inset-0 box-art-shimmer rounded-full" />
            </div>
            <p className="text-xs text-muted-foreground font-medium tracking-wide">No Cover Art</p>
            <div className="mt-2 w-16 h-0.5 bg-gradient-to-r from-transparent via-muted-foreground/20 to-transparent animate-pulse" />
          </div>
        ) : null}

        {/* Image quality indicator (for development/debugging) */}
        {process.env.NODE_ENV === 'development' && imageLoaded && !hasError && (
          <div className="absolute top-1 left-1 px-1 py-0.5 text-xs bg-black/50 text-white rounded opacity-0 group-hover:opacity-100 transition-opacity">
            {mode}
          </div>
        )}
      </div>
    </div>
  );
};

/**
 * Steam-style Game Image Component
 * Optimized for Steam's capsule image format
 */
export const SteamGameImage: React.FC<Omit<EnhancedGameImageProps, 'mode'>> = (props) => {
  return <EnhancedGameImage {...props} mode="steam" />;
};

/**
 * Flexible Game Image Component
 * Prioritizes showing the full image content
 */
export const FlexibleGameImage: React.FC<Omit<EnhancedGameImageProps, 'mode'>> = (props) => {
  return <EnhancedGameImage {...props} mode="flexible" />;
};

/**
 * Traditional Game Image Component with Improvements
 * Uses adaptive mode for backward compatibility with enhancements
 */
export const ImprovedGameImage: React.FC<Omit<EnhancedGameImageProps, 'mode'>> = (props) => {
  return <EnhancedGameImage {...props} mode="adaptive" />;
};

export default EnhancedGameImage;
