/**
 * Steam-inspired Library Card Component
 * Implements the Steam library look with hover effects and detailed overlays
 * Features Steam-style external hover card that appears to the right
 */

import React, { useState, useCallback, useRef, useEffect } from 'react';
import { cn } from '@/lib/utils';
import { Card } from '@/components/ui/base/card';
import { Badge } from '@/components/ui/base/badge';
import { Button } from '@/components/ui/base/button';
import { DropdownMenu, DropdownMenuItem } from '@/components/ui/base/dropdown-menu';
import {
  Play,
  Clock,
  Star,
  Calendar,
  User,
  Tag,
  MoreHorizontal,
  Heart,
  Trophy,
  Gamepad2,
  CheckCircle,
  Pause,
  Trash2,
  Zap
} from '@/lib/icons';
import { UserGameWithDetails } from '@/types/database';
import { useCustomArtwork } from '@/hooks/useCustomArtwork';
import { useGameTags } from '@/hooks/useGameTags';

// Platform-specific styling configuration
const getPlatformStyling = (platform: string) => {
  const platformLower = platform?.toLowerCase() || '';

  if (platformLower.includes('pc') || platformLower.includes('steam') || platformLower.includes('windows')) {
    return {
      borderColor: 'border-blue-500/30',
      bgGradient: 'from-blue-500/10 to-blue-600/5',
      icon: Zap,
      iconColor: 'text-blue-400',
      badgeColor: 'bg-blue-500/20 text-blue-300 border-blue-500/30'
    };
  }

  if (platformLower.includes('xbox')) {
    return {
      borderColor: 'border-green-500/30',
      bgGradient: 'from-green-500/10 to-green-600/5',
      icon: Gamepad2,
      iconColor: 'text-green-400',
      badgeColor: 'bg-green-500/20 text-green-300 border-green-500/30'
    };
  }

  if (platformLower.includes('playstation') || platformLower.includes('ps')) {
    return {
      borderColor: 'border-blue-400/30',
      bgGradient: 'from-blue-400/10 to-blue-500/5',
      icon: Gamepad2,
      iconColor: 'text-blue-300',
      badgeColor: 'bg-blue-400/20 text-blue-200 border-blue-400/30'
    };
  }

  // Default styling for other platforms
  return {
    borderColor: 'border-gray-500/30',
    bgGradient: 'from-gray-500/10 to-gray-600/5',
    icon: Gamepad2,
    iconColor: 'text-gray-400',
    badgeColor: 'bg-gray-500/20 text-gray-300 border-gray-500/30'
  };
};

interface SteamLibraryCardProps {
  gameData: UserGameWithDetails;
  onGameClick: (gameData: UserGameWithDetails) => void;
  onStatusUpdate?: (userGameId: string, status: string) => void;
  onQuickPlay?: (gameData: UserGameWithDetails) => void;
  className?: string;
  showHoverDetails?: boolean;
  enableQuickActions?: boolean;
  showSteamHoverCard?: boolean; // New prop for Steam-style external hover card
}

export const SteamLibraryCard: React.FC<SteamLibraryCardProps> = ({
  gameData,
  onGameClick,
  onStatusUpdate,
  onQuickPlay,
  className,
  showHoverDetails = true,
  enableQuickActions = true,
  showSteamHoverCard = true
}) => {
  const game = gameData.game;
  const [isHovered, setIsHovered] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);
  const [imageError, setImageError] = useState(false);

  const [hoverCardPosition, setHoverCardPosition] = useState({ x: 0, y: 0 });
  const [isWishlisted, setIsWishlisted] = useState(gameData.status === 'wishlist');
  const cardRef = useRef<HTMLDivElement>(null);
  const hoverTimeoutRef = useRef<NodeJS.Timeout>();

  // Get custom artwork for this user game
  const { getBestCoverImage, hasCustomArtwork } = useCustomArtwork(gameData.id);
  const { gameTags } = useGameTags(gameData.id);

  // Get platform-specific styling
  const platformStyle = getPlatformStyling(game.platform || '');

  const handleMouseEnter = useCallback(() => {
    if (hoverTimeoutRef.current) {
      clearTimeout(hoverTimeoutRef.current);
    }

    hoverTimeoutRef.current = setTimeout(() => {
      setIsHovered(true);
      const rect = cardRef.current?.getBoundingClientRect();
      if (rect) {
        // Position for Steam-style hover card (to the right of the card)
        // Check if there's enough space on the right, otherwise position on the left
        const hoverCardWidth = 320; // 80 * 4 (w-80 = 320px)
        const viewportWidth = window.innerWidth;
        const spaceOnRight = viewportWidth - rect.right;
        const spaceOnLeft = rect.left;

        let x: number;
        if (spaceOnRight >= hoverCardWidth + 20) {
          // Position to the right
          x = rect.right + 10;
        } else if (spaceOnLeft >= hoverCardWidth + 20) {
          // Position to the left
          x = rect.left - hoverCardWidth - 10;
        } else {
          // Center on screen if no space on either side
          x = Math.max(10, (viewportWidth - hoverCardWidth) / 2);
        }

        // Ensure the card doesn't go off the top or bottom of the screen
        const hoverCardHeight = 400; // Approximate height
        const viewportHeight = window.innerHeight;
        const y = Math.max(10, Math.min(rect.top, viewportHeight - hoverCardHeight - 10));

        setHoverCardPosition({ x, y });
      }
    }, 300); // Slightly longer delay for Steam-style behavior
  }, []);

  const handleMouseLeave = useCallback(() => {
    if (hoverTimeoutRef.current) {
      clearTimeout(hoverTimeoutRef.current);
    }
    setIsHovered(false);
  }, []);

  const handleMouseMove = useCallback(() => {
    // Mouse move tracking removed for performance
  }, []);

  const handleCardClick = useCallback((e: React.MouseEvent) => {
    // Prevent click if clicking on action buttons
    if ((e.target as HTMLElement).closest('[data-action-button]')) {
      return;
    }
    onGameClick(gameData);
  }, [gameData, onGameClick]);

  const handleQuickPlay = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    onQuickPlay?.(gameData);
  }, [gameData, onQuickPlay]);

  const handleWishlistToggle = useCallback((e: React.MouseEvent) => {
    e.stopPropagation();
    const newStatus = isWishlisted ? 'backlog' : 'wishlist';
    setIsWishlisted(!isWishlisted);
    onStatusUpdate?.(gameData.id, newStatus);
  }, [isWishlisted, gameData.id, onStatusUpdate]);

  const handleStatusUpdate = useCallback((status: string) => {
    onStatusUpdate?.(gameData.id, status);
    if (status === 'wishlist') {
      setIsWishlisted(true);
    } else {
      setIsWishlisted(false);
    }
  }, [gameData.id, onStatusUpdate]);

  const handleImageLoad = useCallback(() => {
    setImageLoaded(true);
  }, []);

  const handleImageError = useCallback(() => {
    setImageError(true);
  }, []);

  useEffect(() => {
    return () => {
      if (hoverTimeoutRef.current) {
        clearTimeout(hoverTimeoutRef.current);
      }
    };
  }, []);

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'playing': return 'bg-green-600';
      case 'completed': return 'bg-blue-600';
      case 'paused': return 'bg-yellow-600';
      case 'dropped': return 'bg-red-600';
      case 'wishlist': return 'bg-purple-600';
      default: return 'bg-gray-600';
    }
  };

  const formatPlayTime = (hours?: number) => {
    if (!hours) return null;
    if (hours < 1) return `${Math.round(hours * 60)}m`;
    if (hours < 100) return `${hours.toFixed(1)}h`;
    return `${Math.round(hours)}h`;
  };

  return (
    <>
      <Card
        ref={cardRef}
        className={cn(
          'group relative overflow-hidden cursor-pointer transition-all duration-300 ease-out',
          'bg-[#1e2328] hover:bg-[#2a3037]',
          `bg-gradient-to-br ${platformStyle.bgGradient}`,
          platformStyle.borderColor,
          `hover:shadow-lg hover:shadow-${platformStyle.iconColor.split('-')[1]}-500/20`,
          `hover:border-${platformStyle.iconColor.split('-')[1]}-500/30`,
          'hover:-translate-y-1 hover:scale-[1.02]',
          className
        )}
        onClick={handleCardClick}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onMouseMove={handleMouseMove}
      >
      {/* Main Image Container */}
      <div className="relative aspect-[3/4] overflow-hidden">
        {/* Loading shimmer */}
        {!imageLoaded && !imageError && (
          <div className="absolute inset-0 bg-gradient-to-r from-gray-800 via-gray-700 to-gray-800 animate-pulse" />
        )}

        {/* Game Image */}
        {(getBestCoverImage() || game.cover_image) && !imageError ? (
          <div className="w-full h-full bg-gray-900 flex items-center justify-center">
            <img
              src={getBestCoverImage() || game.cover_image}
              alt={game.title}
              className={cn(
                'max-w-full max-h-full object-contain transition-all duration-500',
                imageLoaded
                  ? 'opacity-100 group-hover:scale-105 group-hover:brightness-110'
                  : 'opacity-0'
              )}
              onLoad={handleImageLoad}
              onError={handleImageError}
              loading="lazy"
            />
          </div>
        ) : (
          // Fallback for missing images
          <div className="w-full h-full bg-gradient-to-br from-gray-800 to-gray-900 flex items-center justify-center">
            <div className="text-center text-gray-400">
              <Tag className="h-8 w-8 mx-auto mb-2 opacity-50" />
              <p className="text-sm font-medium">{game.title}</p>
            </div>
          </div>
        )}

        {/* Gradient overlays */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/80 via-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
        <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

        {/* Status and Platform indicators */}
        <div className="absolute top-2 left-2 flex flex-col gap-1">
          <Badge
            className={cn(
              'text-white border-0 text-xs px-2 py-1',
              getStatusColor(gameData.status)
            )}
          >
            {gameData.status}
          </Badge>
          {game.platform && (
            <Badge className={cn('text-xs px-2 py-1 flex items-center gap-1', platformStyle.badgeColor)}>
              <platformStyle.icon className="h-3 w-3" />
              {game.platform}
            </Badge>
          )}
        </div>

        {/* Custom artwork indicator */}
        {hasCustomArtwork && (
          <div className="absolute top-2 right-2">
            <Badge className="bg-orange-600/90 text-white border-0 text-xs px-1.5 py-0.5">
              Custom
            </Badge>
          </div>
        )}

        {/* Play time indicator */}
        {gameData.hours_played && (
          <div className="absolute bottom-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            <Badge className="bg-black/70 text-white border-0 text-xs px-2 py-1">
              <Clock className="h-3 w-3 mr-1" />
              {formatPlayTime(gameData.hours_played)}
            </Badge>
          </div>
        )}

        {/* Steam-style hover overlay with game details */}
        {isHovered && showHoverDetails && (
          <div className="absolute inset-0 bg-gradient-to-t from-black/95 via-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none z-10">
            <div className="absolute bottom-0 left-0 right-0 p-4 space-y-3">
              {/* Game title */}
              <h3 className="text-white font-semibold text-lg leading-tight line-clamp-2">
                {game.title}
              </h3>

              {/* Game metadata */}
              <div className="space-y-1 text-sm text-gray-300">
                {game.developer && (
                  <div className="flex items-center gap-1">
                    <User className="h-3 w-3" />
                    <span>{game.developer}</span>
                  </div>
                )}

                {game.release_date && (
                  <div className="flex items-center gap-1">
                    <Calendar className="h-3 w-3" />
                    <span>{new Date(game.release_date).getFullYear()}</span>
                  </div>
                )}

                {gameData.personal_rating && (
                  <div className="flex items-center gap-1">
                    <Star className="h-3 w-3 fill-current text-yellow-500" />
                    <span>Your rating: {gameData.personal_rating}/10</span>
                  </div>
                )}
              </div>

              {/* Game tags */}
              {gameTags.length > 0 && (
                <div className="flex flex-wrap gap-1">
                  {gameTags.slice(0, 3).map((tag) => (
                    <Badge
                      key={tag.id}
                      className="bg-blue-600/80 text-white border-0 text-xs px-1.5 py-0.5"
                    >
                      {tag.name}
                    </Badge>
                  ))}
                  {gameTags.length > 3 && (
                    <Badge className="bg-gray-600/80 text-white border-0 text-xs px-1.5 py-0.5">
                      +{gameTags.length - 3}
                    </Badge>
                  )}
                </div>
              )}
            </div>
          </div>
        )}

        {/* Quick action buttons */}
        {isHovered && enableQuickActions && (
          <div className="absolute top-2 left-1/2 transform -translate-x-1/2 opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-auto z-20">
            <div className="flex gap-2">
              <Button
                size="sm"
                className="bg-green-600 hover:bg-green-700 text-white border-0 h-8 px-3"
                onClick={handleQuickPlay}
                data-action-button
              >
                <Play className="h-3 w-3 mr-1" />
                Play
              </Button>

              <Button
                size="sm"
                variant="secondary"
                className={cn(
                  "h-8 px-2 border-gray-600 transition-colors",
                  isWishlisted
                    ? "bg-red-600/80 hover:bg-red-700/80 text-white"
                    : "bg-black/70 hover:bg-black/80 text-white"
                )}
                onClick={handleWishlistToggle}
                data-action-button
                title={isWishlisted ? "Remove from Wishlist" : "Add to Wishlist"}
              >
                <Heart className={cn("h-3 w-3", isWishlisted && "fill-current")} />
              </Button>

              <DropdownMenu
                trigger={
                  <Button
                    size="sm"
                    variant="secondary"
                    className="bg-black/70 hover:bg-black/80 text-white border-gray-600 h-8 px-2"
                    onClick={(e) => e.stopPropagation()}
                    data-action-button
                  >
                    <MoreHorizontal className="h-3 w-3" />
                  </Button>
                }
                align="end"
              >
                <DropdownMenuItem onClick={() => handleStatusUpdate('playing')}>
                  <Play className="h-4 w-4 mr-2" />
                  Mark as Playing
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleStatusUpdate('completed')}>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Mark as Completed
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleStatusUpdate('backlog')}>
                  <Pause className="h-4 w-4 mr-2" />
                  Move to Backlog
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleStatusUpdate('wishlist')}>
                  <Heart className="h-4 w-4 mr-2" />
                  Add to Wishlist
                </DropdownMenuItem>
                <DropdownMenuItem
                  onClick={() => {
                    // Handle remove from library - could call a prop function
                    console.log('Remove from library:', gameData.game.title);
                  }}
                  className="text-destructive focus:text-destructive"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  Remove from Library
                </DropdownMenuItem>
              </DropdownMenu>
            </div>
          </div>
        )}
      </div>

      {/* Bottom info bar - always visible */}
      <div className="p-3 bg-[#1e2328] border-t border-[#3c4043]">
        <div className="flex items-center justify-between">
          <div className="flex-1 min-w-0">
            <h4 className="text-white font-medium text-sm truncate group-hover:text-blue-400 transition-colors">
              {game.title}
            </h4>
            <p className="text-gray-400 text-xs truncate">
              {game.developer || 'Unknown Developer'}
            </p>
          </div>

          {gameData.hours_played && (
            <div className="text-right">
              <p className="text-gray-300 text-xs">
                {formatPlayTime(gameData.hours_played)}
              </p>
              <p className="text-gray-500 text-xs">played</p>
            </div>
          )}
        </div>
      </div>
    </Card>

    {/* Steam-style External Hover Card */}
    {isHovered && showSteamHoverCard && (
      <div
        className="fixed z-50 pointer-events-none"
        style={{
          left: `${hoverCardPosition.x}px`,
          top: `${hoverCardPosition.y}px`,
        }}
      >
        <Card className="w-80 bg-[#1e2328] border-[#3c4043] shadow-2xl shadow-black/50 animate-in fade-in-0 slide-in-from-left-2 duration-200">
          {/* Header with game image and title */}
          <div className="relative">
            {/* Background image */}
            <div className="h-32 overflow-hidden rounded-t-lg">
              {(getBestCoverImage() || game.cover_image) && !imageError ? (
                <img
                  src={getBestCoverImage() || game.cover_image}
                  alt={game.title}
                  className="w-full h-full object-cover opacity-60"
                />
              ) : (
                <div className="w-full h-full bg-gradient-to-br from-gray-800 to-gray-900" />
              )}
              <div className="absolute inset-0 bg-gradient-to-t from-black/80 to-transparent" />
            </div>

            {/* Game title overlay */}
            <div className="absolute bottom-0 left-0 right-0 p-4">
              <h3 className="text-white font-bold text-lg leading-tight">
                {game.title}
              </h3>
              <p className="text-gray-300 text-sm">
                {game.developer}
              </p>
            </div>
          </div>

          {/* Content */}
          <div className="p-4 space-y-4">
            {/* Time played section */}
            <div className="space-y-2">
              <h4 className="text-gray-400 text-xs uppercase tracking-wide font-medium">
                Time Played
              </h4>
              <div className="space-y-1">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-300">Last two weeks:</span>
                  <span className="text-white">
                    {gameData.hours_played && gameData.hours_played > 0
                      ? `${Math.round((gameData.hours_played * 0.1) * 60)} min`
                      : '0 min'}
                  </span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-300">Total:</span>
                  <span className="text-white">
                    {gameData.hours_played
                      ? `${Math.round(gameData.hours_played * 60)} min`
                      : '0 min'}
                  </span>
                </div>
              </div>
            </div>

            {/* Game info */}
            <div className="space-y-3">
              {/* Release date */}
              {game.release_date && (
                <div className="flex items-center gap-2 text-sm">
                  <Calendar className="h-4 w-4 text-gray-400" />
                  <span className="text-gray-300">Released:</span>
                  <span className="text-white">
                    {new Date(game.release_date).toLocaleDateString()}
                  </span>
                </div>
              )}

              {/* Platform */}
              {game.platform && (
                <div className="flex items-center gap-2 text-sm">
                  <Gamepad2 className="h-4 w-4 text-gray-400" />
                  <span className="text-gray-300">Platform:</span>
                  <Badge
                    className="bg-gray-700 text-gray-200 border-0 text-xs px-1.5 py-0.5"
                  >
                    {game.platform}
                  </Badge>
                </div>
              )}

              {/* Personal rating */}
              {gameData.personal_rating && (
                <div className="flex items-center gap-2 text-sm">
                  <Star className="h-4 w-4 text-yellow-500 fill-current" />
                  <span className="text-gray-300">Your rating:</span>
                  <span className="text-white font-medium">
                    {gameData.personal_rating}/10
                  </span>
                </div>
              )}

              {/* Metacritic score */}
              {game.metacritic_score && (
                <div className="flex items-center gap-2 text-sm">
                  <Trophy className="h-4 w-4 text-orange-500" />
                  <span className="text-gray-300">Metacritic:</span>
                  <span className="text-white font-medium">
                    {game.metacritic_score}/100
                  </span>
                </div>
              )}
            </div>

            {/* Genres */}
            {game.genres && game.genres.length > 0 && (
              <div className="space-y-2">
                <h4 className="text-gray-400 text-xs uppercase tracking-wide font-medium">
                  Genres
                </h4>
                <div className="flex flex-wrap gap-1">
                  {game.genres.map((genre) => (
                    <Badge
                      key={genre}
                      className="bg-blue-600/20 text-blue-300 border-blue-600/30 text-xs px-2 py-0.5"
                    >
                      {genre}
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* User notes */}
            {gameData.notes && (
              <div className="space-y-2">
                <h4 className="text-gray-400 text-xs uppercase tracking-wide font-medium">
                  Your Notes
                </h4>
                <p className="text-gray-300 text-sm italic">
                  "{gameData.notes}"
                </p>
              </div>
            )}

            {/* Quick actions */}
            <div className="flex gap-2 pt-2">
              <Button
                size="sm"
                className="bg-green-600 hover:bg-green-700 text-white border-0 flex-1"
                onClick={(e) => {
                  e.stopPropagation();
                  onQuickPlay?.(gameData);
                }}
              >
                <Play className="h-3 w-3 mr-1" />
                Play
              </Button>
              <Button
                size="sm"
                variant="secondary"
                className="bg-gray-700 hover:bg-gray-600 text-white border-gray-600"
                onClick={(e) => {
                  e.stopPropagation();
                  onGameClick(gameData);
                }}
              >
                Details
              </Button>
            </div>
          </div>
        </Card>
      </div>
    )}
    </>
  );
};
