import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/base/card';
import { Button } from '@/components/ui/base/button';
import { Input } from '@/components/ui/base/input';
import { Badge } from '@/components/ui/base/badge';
import { Progress } from '@/components/ui/base/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/base/tabs';
import { Alert, AlertDescription } from '@/components/ui/base/alert';
import { 
  Download,
  CheckCircle,
  AlertCircle, 
  RefreshCw,
  Gamepad2,
  Trophy,
  Users,
  Clock,
  Shield,
  TrendingUp,
  Star,
  Info
} from 'lucide-react';
import { useSteamImport, formatImportProgress, getImportPhaseColor } from '@/hooks/useSteamImport';
import { cn } from '@/lib/utils';

interface EnhancedSteamImportCardProps {
  className?: string;
}

interface SteamEnhancementStats {
  totalAchievements: number;
  rareAchievements: number;
  friendsImported: number;
  recentlyPlayedGames: number;
  averageCompletionRate: number;
  totalPlaytime2Weeks: number;
}

export const EnhancedSteamImportCard: React.FC<EnhancedSteamImportCardProps> = ({ 
  className
}) => {
  const [steamIdInput, setSteamIdInput] = useState('');
  const [validationError, setValidationError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState('import');
  const {
    importProgress,
    importStats,
    isImporting,
    startImport,
    validateSteamIdInput
  } = useSteamImport();

  const handleSteamIdChange = (value: string) => {
    setSteamIdInput(value);
    setValidationError(null);
  };

  const handleEnhancedImport = async () => {
    const validation = validateSteamIdInput(steamIdInput);
    
    if (!validation.isValid) {
      setValidationError(validation.error || 'Invalid Steam ID');
      return;
    }

    setValidationError(null);
    
    // Start enhanced import with achievements and social features
    await startImport(steamIdInput, {
      includeAchievements: true,
      includeFriends: true,
      includeRecentlyPlayed: true,
      includePlayerBans: true
    });
  };

  const mockEnhancementStats: SteamEnhancementStats = {
    totalAchievements: 1247,
    rareAchievements: 89,
    friendsImported: 23,
    recentlyPlayedGames: 8,
    averageCompletionRate: 67.3,
    totalPlaytime2Weeks: 42
  };

  return (
    <Card className={cn("h-full", className)}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Gamepad2 className="h-5 w-5 text-[#1b2838]" />
            Enhanced Steam Import
          </div>
          {importStats && importStats.steamGames > 0 && (
            <Badge variant="secondary" className="bg-green-100 text-green-800">
              {importStats.steamGames} games imported
            </Badge>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="import">Import</TabsTrigger>
            <TabsTrigger value="achievements">Achievements</TabsTrigger>
            <TabsTrigger value="social">Social</TabsTrigger>
            <TabsTrigger value="analytics">Analytics</TabsTrigger>
          </TabsList>

          <TabsContent value="import" className="space-y-6">
            {/* Enhanced Import Statistics */}
            {importStats && (
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 p-4 bg-muted/50 rounded-lg">
                <div className="text-center">
                  <div className="text-2xl font-bold text-[#1b2838]">
                    {importStats.steamGames}
                  </div>
                  <p className="text-xs text-muted-foreground">Steam Games</p>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-yellow-600">
                    {mockEnhancementStats.totalAchievements}
                  </div>
                  <p className="text-xs text-muted-foreground">Achievements</p>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">
                    {mockEnhancementStats.friendsImported}
                  </div>
                  <p className="text-xs text-muted-foreground">Friends</p>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">
                    {mockEnhancementStats.totalPlaytime2Weeks}h
                  </div>
                  <p className="text-xs text-muted-foreground">Recent Play</p>
                </div>
              </div>
            )}

            {/* Enhanced Import Progress */}
            {importProgress && (
              <div className="space-y-3 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <div className="flex items-center justify-between">
                  <p className={cn("font-medium", getImportPhaseColor(importProgress.phase))}>
                    {formatImportProgress(importProgress)}
                  </p>
                  <span className="text-sm font-mono">
                    {Math.round(importProgress.progress)}%
                  </span>
                </div>
                <Progress value={importProgress.progress} className="h-2" />
                {importProgress.gamesProcessed && importProgress.totalGames && (
                  <div className="flex justify-between text-sm text-muted-foreground">
                    <span>{importProgress.gamesProcessed} of {importProgress.totalGames} games processed</span>
                    <span>Fetching achievements & social data...</span>
                  </div>
                )}
              </div>
            )}

            {/* Steam ID Input */}
            <div className="space-y-3">
              <div>
                <label className="text-sm font-medium mb-2 block">
                  Steam Profile
                </label>
                <div className="space-y-2">
                  <Input
                    value={steamIdInput}
                    onChange={(e) => handleSteamIdChange(e.target.value)}
                    placeholder="Enter Steam ID, custom URL, or profile URL"
                    disabled={isImporting}
                    className={validationError ? 'border-red-500' : ''}
                  />
                  {validationError && (
                    <p className="text-sm text-red-600 flex items-center gap-1">
                      <AlertCircle className="h-3 w-3" />
                      {validationError}
                    </p>
                  )}
                </div>
              </div>

              <Button
                onClick={handleEnhancedImport}
                disabled={isImporting || !steamIdInput.trim()}
                className="w-full"
                size="lg"
              >
                {isImporting ? (
                  <>
                    <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    Importing Enhanced Data...
                  </>
                ) : (
                  <>
                    <Download className="h-4 w-4 mr-2" />
                    Enhanced Steam Import
                  </>
                )}
              </Button>
            </div>

            {/* Enhancement Features Info */}
            <Alert>
              <Info className="h-4 w-4" />
              <AlertDescription>
                Enhanced import includes: Achievement tracking, Friends list, Recently played games, 
                Player statistics, and Ban information for comprehensive library management.
              </AlertDescription>
            </Alert>
          </TabsContent>

          <TabsContent value="achievements" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm flex items-center gap-2">
                    <Trophy className="h-4 w-4 text-yellow-500" />
                    Achievement Overview
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-sm">Total Achievements</span>
                      <span className="font-bold">{mockEnhancementStats.totalAchievements}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Rare Achievements</span>
                      <span className="font-bold text-purple-600">{mockEnhancementStats.rareAchievements}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Completion Rate</span>
                      <span className="font-bold text-green-600">{mockEnhancementStats.averageCompletionRate}%</span>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm flex items-center gap-2">
                    <Star className="h-4 w-4 text-yellow-500" />
                    Recent Achievements
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2 text-sm">
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary" className="text-xs">Rare</Badge>
                      <span>Master Strategist</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="text-xs">Common</Badge>
                      <span>First Victory</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Badge variant="secondary" className="text-xs">Rare</Badge>
                      <span>Speed Runner</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="social" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm flex items-center gap-2">
                    <Users className="h-4 w-4 text-blue-500" />
                    Steam Friends
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-sm">Friends Imported</span>
                      <span className="font-bold">{mockEnhancementStats.friendsImported}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Common Games</span>
                      <span className="font-bold text-green-600">156</span>
                    </div>
                    <Button variant="outline" size="sm" className="w-full">
                      <Users className="h-3 w-3 mr-1" />
                      Compare Libraries
                    </Button>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm flex items-center gap-2">
                    <Shield className="h-4 w-4 text-green-500" />
                    Account Status
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-3 w-3 text-green-500" />
                      <span className="text-sm">No VAC Bans</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-3 w-3 text-green-500" />
                      <span className="text-sm">No Community Bans</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-3 w-3 text-green-500" />
                      <span className="text-sm">Good Standing</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          <TabsContent value="analytics" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm flex items-center gap-2">
                    <Clock className="h-4 w-4 text-orange-500" />
                    Recent Activity
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-sm">Recently Played</span>
                      <span className="font-bold">{mockEnhancementStats.recentlyPlayedGames} games</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">2-Week Playtime</span>
                      <span className="font-bold text-blue-600">{mockEnhancementStats.totalPlaytime2Weeks}h</span>
                    </div>
                    <Progress value={75} className="h-2" />
                    <p className="text-xs text-muted-foreground">75% more active than last month</p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-sm flex items-center gap-2">
                    <TrendingUp className="h-4 w-4 text-green-500" />
                    Gaming Trends
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Most Played Genre</span>
                      <span className="font-medium">Action</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Avg. Session Length</span>
                      <span className="font-medium">2.3h</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Completion Rate</span>
                      <span className="font-medium text-green-600">67%</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};
