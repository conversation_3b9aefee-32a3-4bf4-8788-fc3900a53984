import { useState, useEffect } from 'react';
import { Monitor, Moon, Sun } from 'lucide-react';
import { Button } from './base/button';
import { Tooltip, TooltipContent, TooltipTrigger } from './base/tooltip';
import { useTheme } from '../../hooks/useTheme';

interface ThemeToggleProps {
  size?: 'sm' | 'md' | 'lg';
  showLabel?: boolean;
  className?: string;
}

export function ThemeToggle({
  size = 'md',
  showLabel = false,
  className = ''
}: ThemeToggleProps) {
  const { theme, setTheme, systemTheme, isLoading } = useTheme();
  const [mounted, setMounted] = useState(false);

  // Prevent hydration mismatch
  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted || isLoading) {
    return (
      <Button
        variant="ghost"
        size={size === 'sm' ? 'sm' : size === 'lg' ? 'lg' : 'icon'}
        className={`transition-all duration-200 ${
          size === 'sm' ? 'h-8 w-8' : size === 'lg' ? 'h-10 w-10' : 'h-9 w-9'
        } ${className}`}
        disabled
        aria-label="Loading theme toggle"
      >
        <div className="h-4 w-4 animate-pulse bg-muted rounded" />
      </Button>
    );
  }

  const getThemeIcon = (themeType: string) => {
    switch (themeType) {
      case 'light':
        return <Sun className="h-4 w-4" />;
      case 'dark':
        return <Moon className="h-4 w-4" />;
      case 'system':
        return <Monitor className="h-4 w-4" />;
      default:
        return <Monitor className="h-4 w-4" />;
    }
  };

  const getThemeLabel = (themeType: string) => {
    switch (themeType) {
      case 'light':
        return 'Light mode';
      case 'dark':
        return 'Dark mode';
      case 'system':
        return `System (${systemTheme})`;
      default:
        return 'System';
    }
  };

  const getNextTheme = () => {
    switch (theme) {
      case 'light':
        return 'dark';
      case 'dark':
        return 'system';
      case 'system':
        return 'light';
      default:
        return 'light';
    }
  };

  const handleToggle = () => {
    const nextTheme = getNextTheme();
    setTheme(nextTheme);

    // Announce theme change for screen readers
    const announcement = `Theme changed to ${getThemeLabel(nextTheme)}`;
    const announcer = document.createElement('div');
    announcer.setAttribute('aria-live', 'polite');
    announcer.setAttribute('aria-atomic', 'true');
    announcer.className = 'sr-only';
    announcer.textContent = announcement;
    document.body.appendChild(announcer);
    setTimeout(() => document.body.removeChild(announcer), 1000);
  };

  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <Button
          variant="ghost"
          size={size === 'sm' ? 'sm' : size === 'lg' ? 'lg' : 'icon'}
          className={`transition-all duration-200 hover:bg-accent/50 hover:scale-105 focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 ${
            size === 'sm' ? 'h-8 w-8' : size === 'lg' ? 'h-10 w-10' : 'h-9 w-9'
          } ${showLabel ? 'gap-2 px-3' : ''} ${className}`}
          onClick={handleToggle}
          aria-label={`Switch to ${getThemeLabel(getNextTheme())}`}
        >
          <span className="transition-transform duration-200 hover:rotate-12">
            {getThemeIcon(theme)}
          </span>
          {showLabel && (
            <span className="text-sm font-medium">
              {getThemeLabel(theme)}
            </span>
          )}
        </Button>
      </TooltipTrigger>
      <TooltipContent side="bottom" className="font-medium">
        <p>Current: {getThemeLabel(theme)}</p>
        <p className="text-xs text-muted-foreground">
          Click to switch to {getThemeLabel(getNextTheme())}
        </p>
      </TooltipContent>
    </Tooltip>
  );
}

// Compact theme toggle for mobile or space-constrained areas
export function CompactThemeToggle({ className }: { className?: string }) {
  return (
    <ThemeToggle
      size="sm"
      className={className}
    />
  );
}

// Theme toggle with label for settings pages
export function LabeledThemeToggle({ className }: { className?: string }) {
  return (
    <ThemeToggle
      size="md"
      showLabel
      className={className}
    />
  );
}
