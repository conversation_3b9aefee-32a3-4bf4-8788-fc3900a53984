/**
 * DEMO COMPONENT - Production Library with Steam View Mode
 * 
 * This component demonstrates the Steam view mode integration into the production library
 * showing how the Steam hover cards work alongside the existing library functionality.
 */

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/base/card';
import { Button } from '@/components/ui/base/button';
import { Badge } from '@/components/ui/base/badge';
import { SteamLibraryCard } from '@/components/ui/game/steam-library-card';
import { UnifiedGameCard } from '@/components/ui/game/unified-game-card';
import { UserGameWithDetails } from '@/types/database';
import {
  Grid3X3,
  Layers,
  Users,
  Gamepad2,
  Search
} from '@/lib/icons';

// Sample data matching the production library structure
const sampleLibraryGames: UserGameWithDetails[] = [
  {
    id: 'demo-1',
    user_id: 'demo-user',
    game_id: 'game-1',
    status: 'completed',
    rating: 5,
    personal_notes: 'Amazing open-world RPG with incredible storytelling.',
    time_played: 7638,
    last_played: '2024-01-15',
    platform: 'PC',
    created_at: '2024-01-01',
    updated_at: '2024-01-15',
    game: {
      id: 'game-1',
      title: 'The Witcher 3: Wild Hunt',
      description: 'A story-driven open world RPG set in a visually stunning fantasy universe.',
      release_date: '2015-05-19',
      developer: 'CD Projekt RED',
      publisher: 'CD Projekt',
      platforms: ['PC', 'PlayStation 4', 'Xbox One', 'Nintendo Switch'],
      genres: ['RPG', 'Fantasy', 'Open World'],
      metacritic_score: 93,
      cover_image: 'https://images.igdb.com/igdb/image/upload/t_cover_big/co1wyy.webp',
      background_image: 'https://images.igdb.com/igdb/image/upload/t_1080p/sc58zu.webp',
      created_at: '2024-01-01',
      updated_at: '2024-01-01'
    }
  },
  {
    id: 'demo-2',
    user_id: 'demo-user',
    game_id: 'game-2',
    status: 'playing',
    rating: 4,
    personal_notes: 'Incredible atmosphere and gameplay mechanics.',
    time_played: 5340,
    last_played: '2024-01-20',
    platform: 'PC',
    created_at: '2024-01-01',
    updated_at: '2024-01-20',
    game: {
      id: 'game-2',
      title: 'Hades',
      description: 'A rogue-like dungeon crawler from the creators of Bastion and Transistor.',
      release_date: '2020-09-17',
      developer: 'Supergiant Games',
      publisher: 'Supergiant Games',
      platforms: ['PC', 'PlayStation 4', 'PlayStation 5', 'Xbox One', 'Xbox Series X/S', 'Nintendo Switch'],
      genres: ['Action', 'Rogue-like', 'Indie'],
      metacritic_score: 93,
      cover_image: 'https://images.igdb.com/igdb/image/upload/t_cover_big/co2i2x.webp',
      background_image: 'https://images.igdb.com/igdb/image/upload/t_1080p/sc6xvz.webp',
      created_at: '2024-01-01',
      updated_at: '2024-01-01'
    }
  },
  {
    id: 'demo-3',
    user_id: 'demo-user',
    game_id: 'game-3',
    status: 'backlog',
    rating: null,
    personal_notes: null,
    time_played: 2850,
    last_played: '2024-01-10',
    platform: 'PC',
    created_at: '2024-01-01',
    updated_at: '2024-01-10',
    game: {
      id: 'game-3',
      title: 'Cyberpunk 2077',
      description: 'An open-world action-adventure RPG set in the megalopolis of Night City.',
      release_date: '2020-12-10',
      developer: 'CD Projekt RED',
      publisher: 'CD Projekt',
      platforms: ['PC', 'PlayStation 4', 'PlayStation 5', 'Xbox One', 'Xbox Series X/S'],
      genres: ['Action', 'RPG', 'Open World'],
      metacritic_score: 86,
      cover_image: 'https://images.igdb.com/igdb/image/upload/t_cover_big/co2dpv.webp',
      background_image: 'https://images.igdb.com/igdb/image/upload/t_1080p/sc6wxy.webp',
      created_at: '2024-01-01',
      updated_at: '2024-01-01'
    }
  }
];

type ViewMode = "status" | "platform" | "platform-family" | "steam";

export function ProductionLibraryDemo() {
  const [viewMode, setViewMode] = useState<ViewMode>('steam');
  const [searchQuery, setSearchQuery] = useState('');

  const handleGameClick = (gameData: UserGameWithDetails) => {
    console.log('Game clicked:', gameData.game.title);
  };

  const handleStatusUpdate = (gameId: string, status: string) => {
    console.log('Status update:', gameId, status);
  };

  const handleQuickPlay = (gameData: UserGameWithDetails) => {
    console.log('Quick play:', gameData.game.title);
  };

  const filteredGames = sampleLibraryGames.filter(game =>
    game.game.title.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const getStatsForStatus = (status: string) => {
    return sampleLibraryGames.filter(game => game.status === status).length;
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <div className="border-b bg-card">
        <div className="container mx-auto px-4 py-6">
          <div className="flex flex-col gap-4">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold">My Library</h1>
                <p className="text-muted-foreground">Track your gaming collection</p>
              </div>
              <div className="flex items-center gap-2">
                <Badge variant="secondary">{sampleLibraryGames.length} games</Badge>
                <Badge variant="outline">Status</Badge>
                <Badge variant="default">Platform</Badge>
                <Badge variant="outline">Platform Family</Badge>
              </div>
            </div>

            {/* View Mode Buttons */}
            <div className="flex items-center gap-2">
              <Button
                variant={viewMode === 'status' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('status')}
                className="h-8 flex-shrink-0"
              >
                <Layers className="h-4 w-4 mr-1" />
                <span className="hidden sm:inline">Status</span>
              </Button>
              <Button
                variant={viewMode === 'platform' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('platform')}
                className="h-8 flex-shrink-0"
              >
                <Grid3X3 className="h-4 w-4 mr-1" />
                <span className="hidden sm:inline">Platform</span>
              </Button>
              <Button
                variant={viewMode === 'platform-family' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('platform-family')}
                className="h-8 flex-shrink-0"
              >
                <Users className="h-4 w-4 mr-1" />
                <span className="hidden sm:inline">Platform Family</span>
              </Button>
              <Button
                variant={viewMode === 'steam' ? 'default' : 'ghost'}
                size="sm"
                onClick={() => setViewMode('steam')}
                className="h-8 flex-shrink-0"
              >
                <Gamepad2 className="h-4 w-4 mr-1" />
                <span className="hidden sm:inline">Steam</span>
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="container mx-auto px-4 py-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
          <Card>
            <CardContent className="pt-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-orange-500">{getStatsForStatus('playing')}</div>
                <div className="text-sm text-muted-foreground">Playing</div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-green-500">{getStatsForStatus('completed')}</div>
                <div className="text-sm text-muted-foreground">Completed</div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="pt-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-500">{getStatsForStatus('backlog')}</div>
                <div className="text-sm text-muted-foreground">Backlog</div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Search Bar */}
        <div className="mb-6">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <input
              type="text"
              placeholder="Search your library..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-input bg-background rounded-md focus:outline-none focus:ring-2 focus:ring-ring"
            />
          </div>
        </div>

        {/* Game Grid */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              {viewMode === 'steam' ? (
                <>
                  <Gamepad2 className="h-5 w-5" />
                  Steam Library View
                </>
              ) : (
                <>
                  <Grid3X3 className="h-5 w-5" />
                  Standard Library View
                </>
              )}
            </CardTitle>
            <p className="text-muted-foreground">
              {viewMode === 'steam' 
                ? 'Steam-style cards with external hover details and quick actions'
                : 'Standard unified game cards'
              }
            </p>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6">
              {filteredGames.map((gameData) => (
                viewMode === 'steam' ? (
                  <SteamLibraryCard
                    key={gameData.id}
                    gameData={gameData}
                    onGameClick={handleGameClick}
                    onStatusUpdate={handleStatusUpdate}
                    onQuickPlay={handleQuickPlay}
                    showHoverDetails={true}
                    enableQuickActions={true}
                    showSteamHoverCard={true}
                  />
                ) : (
                  <UnifiedGameCard
                    key={gameData.id}
                    gameData={gameData}
                    onGameClick={handleGameClick}
                    onStatusUpdate={handleStatusUpdate}
                    onRemoveGame={(gameId) => console.log('Remove game:', gameId)}
                    showQuickActions={true}
                    showPlatformIcon={true}
                    showRating={true}
                    showPlaytime={true}
                  />
                )
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
